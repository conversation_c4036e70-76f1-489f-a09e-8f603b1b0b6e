import {cskh_api} from "@/lib/cskh.api.ts";

export interface Chatbot {
  _id: string;
  defaultPrompt: string;
  openaiApiKey: string;
  modelName: string;
  temperature: number;
  historyMessageCount: number;
  createdAt: string;
  updatedAt: string;
}


export interface ChatbotUpdate {
  defaultPrompt: string;
  openaiApiKey: string;
  modelName: string;
  temperature: number;
  historyMessageCount: number;
}

export interface ConfigSectionProps {
  title: string;
  description: string;
  children: React.ReactNode;
}


const API = {
  CHATBOT: '/chatbot',
  CHATBOT_ID: '/chatbot/:chatbotId',
};

class OpenAIService {

  async getConfigChatbot(): Promise<Chatbot> {
    return cskh_api.get(API.CHATBOT);
  }

  async updateConfigChatbot(chatbotId: string, data: ChatbotUpdate): Promise<Chatbot> {
    const url = API.CHATBOT_ID.replace(':chatbotId', chatbotId);
    return cskh_api.put(url, data);
  }

}

export const chatbotService = new OpenAIService();