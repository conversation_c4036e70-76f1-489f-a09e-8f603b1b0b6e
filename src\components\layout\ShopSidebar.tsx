import { useState } from "react";
import { NavLink, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useAuth } from "@/contexts/AuthContext";
import {
  Menu,
  X,
  LayoutDashboard,
  MessageSquare,
  PhoneCall,
  History,
  Bell,
  Package
} from "lucide-react";

export function ShopSidebar({ isCollapsed, onToggle }: { isCollapsed?: boolean, onToggle?: () => void }) {
  const { logout } = useAuth();
  const [collapsed, setCollapsed] = useState(isCollapsed || false);
  const [mobileOpen, setMobileOpen] = useState(false);
  const location = useLocation();

  // Sử dụng prop isCollapsed nếu được cung cấp
  const effectiveCollapsed = isCollapsed !== undefined ? isCollapsed : collapsed;

  const toggleSidebar = () => {
    if (onToggle) {
      onToggle();
    } else {
      setCollapsed(!collapsed);
    }
  };

  const toggleMobile = () => {
    setMobileOpen(!mobileOpen);
  };

  return (
    <>
      {/* Mobile toggle button */}
      <Button
        variant="outline"
        size="icon"
        className="fixed left-1 top-4 z-40 lg:hidden"
        onClick={toggleMobile}
      >
        <Menu />
      </Button>

      {/* Mobile overlay */}
      {mobileOpen && (
        <div
          className="fixed inset-0 z-20 bg-black/50 lg:hidden"
          onClick={() => setMobileOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-30 flex h-screen w-64 flex-col bg-sidebar border-r border-border shadow-sm transition-all duration-300",
          {
            "-translate-x-full": !mobileOpen && effectiveCollapsed,
            "translate-x-0": mobileOpen || !effectiveCollapsed,
            "lg:translate-x-0 lg:w-20": effectiveCollapsed && !mobileOpen,
          }
        )}
      >
        {/* Logo */}
        <div className="flex h-16 items-center justify-center border-b border-border px-4">
          <div className="flex items-center justify-center">
            <div className={cn("app-logo rounded-md p-1", {
              "w-12 h-12": effectiveCollapsed && !mobileOpen,
              "w-24 h-24": !effectiveCollapsed || mobileOpen
            })}>
              <img
                src="/assets/images/logo/logo_ghvn.png"
                alt="GHVN Logo"
                className="w-full h-full object-contain"
              />
            </div>
          </div>
        </div>

        {/* Navigation menu */}
        <div className="flex flex-col gap-1 flex-1 overflow-y-auto p-4">
          <div className="h-2"></div>

          <NavLink
            to="/shop/dashboard"
            className={({ isActive }) =>
              cn("sidebar-link", {
                active: isActive,
              })
            }
            onClick={() => setMobileOpen(false)}
          >
            <LayoutDashboard className="h-5 w-5" />
            {(!effectiveCollapsed || mobileOpen) && <span>Trang chính</span>}
          </NavLink>

          <NavLink
            to="/shop/orders"
            className={({ isActive }) =>
              cn("sidebar-link", {
                active: isActive,
              })
            }
            onClick={() => setMobileOpen(false)}
          >
            <Package className="h-5 w-5" />
            {(!effectiveCollapsed || mobileOpen) && <span>Đơn hàng của tôi</span>}
          </NavLink>

          <NavLink
            to="/shop/support"
            className={({ isActive }) =>
              cn("sidebar-link", {
                active: isActive,
              })
            }
            onClick={() => setMobileOpen(false)}
          >
            <MessageSquare className="h-5 w-5" />
            {(!effectiveCollapsed || mobileOpen) && <span>Hỗ trợ CSKH</span>}
          </NavLink>

          <NavLink
            to="/shop/call"
            className={({ isActive }) =>
              cn("sidebar-link", {
                active: isActive,
              })
            }
            onClick={() => setMobileOpen(false)}
          >
            <PhoneCall className="h-5 w-5" />
            {(!effectiveCollapsed || mobileOpen) && <span>Gọi tổng đài</span>}
          </NavLink>

          <NavLink
            to="/shop/history"
            className={({ isActive }) =>
              cn("sidebar-link", {
                active: isActive,
              })
            }
            onClick={() => setMobileOpen(false)}
          >
            <History className="h-5 w-5" />
            {(!effectiveCollapsed || mobileOpen) && <span>Lịch sử hỗ trợ</span>}
          </NavLink>

          <NavLink
            to="/shop/notifications"
            className={({ isActive }) =>
              cn("sidebar-link", {
                active: isActive,
              })
            }
            onClick={() => setMobileOpen(false)}
          >
            <Bell className="h-5 w-5" />
            {(!effectiveCollapsed || mobileOpen) && <span>Thông báo</span>}
          </NavLink>
        </div>

        {/* Mobile close button */}
        {mobileOpen && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-4 right-4 lg:hidden"
            onClick={toggleMobile}
          >
            <X className="h-5 w-5" />
          </Button>
        )}
      </div>

      {/* Mobile overlay */}
      {mobileOpen && (
        <div
          className="fixed inset-0 z-20 bg-black/50 lg:hidden"
          onClick={toggleMobile}
        />
      )}
    </>
  );
}
