import { api } from "@/lib/api";
import { BaseService } from "./BaseService";

/**
 * Interface cho đơn hàng của shop
 */
export interface ShopOrder {
  _id: string;
  orderCode: string;
  customerName: string;
  customerPhone: string;
  customerAddress: string;
  products: Array<{
    name: string;
    quantity: number;
    price: number;
  }>;
  totalAmount: number;
  codAmount: number;
  status: 'pending' | 'confirmed' | 'shipping' | 'delivered' | 'cancelled' | 'returned';
  createdAt: string;
  updatedAt: string;
  deliveryDate?: string;
  trackingCode?: string;
  notes?: string;
}

/**
 * Interface cho thông báo của shop
 */
export interface ShopNotification {
  _id: string;
  type: 'order' | 'support' | 'system' | 'promotion';
  title: string;
  message: string;
  isRead: boolean;
  createdAt: string;
  actionUrl?: string;
  priority: 'low' | 'medium' | 'high';
}

/**
 * Interface cho yêu cầu hỗ trợ của shop
 */
export interface ShopSupportRequest {
  _id: string;
  type: 'chat' | 'call';
  title: string;
  description: string;
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high';
  createdAt: string;
  updatedAt: string;
  assignedAgent?: {
    _id: string;
    fullName: string;
    email: string;
  };
  rating?: number;
  feedback?: string;
  duration?: string; // For call requests
}

/**
 * Interface cho dashboard metrics của shop
 */
export interface ShopDashboardMetrics {
  totalOrders: number;
  pendingOrders: number;
  completedOrders: number;
  cancelledOrders: number;
  totalRevenue: number;
  pendingSupportRequests: number;
  unreadNotifications: number;
}

/**
 * Interface cho tham số tìm kiếm đơn hàng
 */
export interface ShopOrderSearchParams {
  query?: string;
  status?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  dateFrom?: string;
  dateTo?: string;
}

/**
 * Interface cho tham số tìm kiếm yêu cầu hỗ trợ
 */
export interface ShopSupportSearchParams {
  query?: string;
  type?: 'chat' | 'call';
  status?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * ShopService - Quản lý các API dành riêng cho shop
 */
class ShopService extends BaseService<any> {
  constructor() {
    super('/shop');
  }

  /**
   * Lấy dashboard metrics cho shop
   */
  async getDashboardMetrics(): Promise<ShopDashboardMetrics> {
    return api.get<ShopDashboardMetrics>('/shop/dashboard/metrics');
  }

  /**
   * Lấy danh sách đơn hàng của shop
   */
  async getOrders(params: ShopOrderSearchParams = {}): Promise<PaginatedResult<ShopOrder>> {
    return api.get<PaginatedResult<ShopOrder>>('/shop/orders', { params: params as Record<string, string> });
  }

  /**
   * Lấy chi tiết đơn hàng
   */
  async getOrderById(orderId: string): Promise<ShopOrder> {
    return api.get<ShopOrder>(`/shop/orders/${orderId}`);
  }

  /**
   * Cập nhật đơn hàng
   */
  async updateOrder(orderId: string, data: Partial<ShopOrder>): Promise<ShopOrder> {
    return api.put<ShopOrder>(`/shop/orders/${orderId}`, data);
  }

  /**
   * Hủy đơn hàng
   */
  async cancelOrder(orderId: string, reason?: string): Promise<ShopOrder> {
    return api.post<ShopOrder>(`/shop/orders/${orderId}/cancel`, { reason });
  }

  /**
   * Lấy danh sách thông báo
   */
  async getNotifications(params: { page?: number; limit?: number; type?: string; isRead?: boolean } = {}): Promise<PaginatedResult<ShopNotification>> {
    return api.get<PaginatedResult<ShopNotification>>('/shop/notifications', { params: params as Record<string, string> });
  }

  /**
   * Đánh dấu thông báo đã đọc
   */
  async markNotificationAsRead(notificationId: string): Promise<ShopNotification> {
    return api.patch<ShopNotification>(`/shop/notifications/${notificationId}/read`);
  }

  /**
   * Đánh dấu tất cả thông báo đã đọc
   */
  async markAllNotificationsAsRead(): Promise<{ success: boolean }> {
    return api.post<{ success: boolean }>('/shop/notifications/mark-all-read');
  }

  /**
   * Xóa thông báo
   */
  async deleteNotification(notificationId: string): Promise<{ success: boolean }> {
    return api.delete<{ success: boolean }>(`/shop/notifications/${notificationId}`);
  }

  /**
   * Lấy danh sách yêu cầu hỗ trợ
   */
  async getSupportRequests(params: ShopSupportSearchParams = {}): Promise<PaginatedResult<ShopSupportRequest>> {
    return api.get<PaginatedResult<ShopSupportRequest>>('/shop/support/requests', { params: params as Record<string, string> });
  }

  /**
   * Tạo yêu cầu hỗ trợ mới
   */
  async createSupportRequest(data: {
    type: 'chat' | 'call';
    title: string;
    description: string;
    priority?: 'low' | 'medium' | 'high';
    relatedOrderId?: string;
  }): Promise<ShopSupportRequest> {
    return api.post<ShopSupportRequest>('/shop/support/requests', data);
  }

  /**
   * Cập nhật yêu cầu hỗ trợ
   */
  async updateSupportRequest(requestId: string, data: Partial<ShopSupportRequest>): Promise<ShopSupportRequest> {
    return api.put<ShopSupportRequest>(`/shop/support/requests/${requestId}`, data);
  }

  /**
   * Đánh giá yêu cầu hỗ trợ
   */
  async rateSupportRequest(requestId: string, rating: number, feedback?: string): Promise<ShopSupportRequest> {
    return api.post<ShopSupportRequest>(`/shop/support/requests/${requestId}/rate`, { rating, feedback });
  }

  /**
   * Lấy thống kê đơn hàng theo thời gian
   */
  async getOrderStats(params: { period: 'week' | 'month' | 'year'; year?: number; month?: number }): Promise<any> {
    return api.get('/shop/orders/stats', { params });
  }

  /**
   * Lấy top sản phẩm bán chạy
   */
  async getTopProducts(params: { limit?: number; period?: 'week' | 'month' | 'year' } = {}): Promise<any[]> {
    return api.get('/shop/products/top', { params });
  }

  /**
   * Tìm kiếm đơn hàng theo mã tracking
   */
  async trackOrder(trackingCode: string): Promise<ShopOrder> {
    return api.get<ShopOrder>(`/shop/orders/track/${trackingCode}`);
  }

  /**
   * Lấy lịch sử cuộc gọi
   */
  async getCallHistory(params: { page?: number; limit?: number } = {}): Promise<PaginatedResult<any>> {
    return api.get('/shop/calls/history', { params });
  }

  /**
   * Bắt đầu cuộc gọi
   */
  async initiateCall(type: 'ai' | 'agent', notes?: string): Promise<{ callId: string; sessionId: string }> {
    return api.post('/shop/calls/initiate', { type, notes });
  }

  /**
   * Kết thúc cuộc gọi
   */
  async endCall(callId: string, duration: number, notes?: string, rating?: number): Promise<{ success: boolean }> {
    return api.post(`/shop/calls/${callId}/end`, { duration, notes, rating });
  }
}

// Export instance của service
export const shopService = new ShopService();

// Export interface để sử dụng ở nơi khác
export type { ShopOrder, ShopNotification, ShopSupportRequest, ShopDashboardMetrics };
