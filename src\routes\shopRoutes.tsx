import { Route } from "react-router-dom";
import { RoleBasedRoute } from "@/components/auth/RoleBasedRoute";

// Customer Pages (shared between shop and buyer)
import CustomerDashboard from "@/pages/customer/CustomerDashboard";
import CustomerCall from "@/pages/customer/CustomerCall";
import CustomerChat from "@/pages/customer-care/customer";
import CustomerHistory from "@/pages/customer/CustomerHistory";
import ProfilePage from "@/pages/profile";
import SupportHistory from "@/pages/customer-care/history";

export const ShopRoutes = (
  <Route element={<RoleBasedRoute allowedRoles={["shop"]} redirectTo="/dashboard" />}>
    <Route path="/shop/dashboard" element={<CustomerDashboard />} />
    <Route path="/shop/call" element={<CustomerCall />} />
    <Route path="/shop/support" element={<CustomerChat />} />
    <Route path="/shop/history" element={<CustomerHistory />} />
    <Route path="/shop/profile" element={<ProfilePage />} />
    <Route path="/shop/support-history" element={<SupportHistory />} />
  </Route>
);
