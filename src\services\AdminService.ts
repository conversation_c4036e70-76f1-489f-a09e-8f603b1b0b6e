import { BaseService, PaginatedResult, PaginationParams } from "./BaseService";
import { User } from "./UserService";

/**
 * Interface cho tham số tìm kiếm admin
 */
export interface AdminSearchParams extends PaginationParams {
  role?: string;
  status?: string;
}

/**
 * AdminService - Quản lý các API liên quan đến quản trị viên
 * Kế thừa từ BaseService để sử dụng các method CRUD cơ bản
 */
class AdminService extends BaseService<User> {
  constructor() {
    super('/admins');
  }

  /**
   * L<PERSON>y danh sách quản trị viên có phân trang
   * @param params Tham số tìm kiếm và phân trang
   * @returns Kết quả phân trang với danh sách quản trị viên
   */
  async getAdmins(params: AdminSearchParams = {}): Promise<PaginatedResult<User>> {
    return this.getAllPaginateBase(params);
  }

  /**
   * L<PERSON>y thông tin chi tiết của một quản trị viên
   * @param id ID của quản trị viên
   * @returns Thông tin chi tiết quản trị viên
   */
  async getAdminById(id: string): Promise<User> {
    return this.getBase(id);
  }

  /**
   * Tạo quản trị viên mới
   * @param adminData Dữ liệu quản trị viên mới
   * @returns Thông tin quản trị viên đã tạo
   */
  async createAdmin(adminData: Partial<User>): Promise<User> {
    return this.createBase(adminData);
  }

  /**
   * Cập nhật thông tin quản trị viên
   * @param id ID của quản trị viên
   * @param adminData Dữ liệu cập nhật
   * @returns Thông tin quản trị viên sau khi cập nhật
   */
  async updateAdmin(id: string, adminData: Partial<User>): Promise<User> {
    return this.updateBase(id, adminData);
  }

  /**
   * Xóa quản trị viên
   * @param id ID của quản trị viên
   * @returns Kết quả xóa
   */
  async deleteAdmin(id: string): Promise<{ success: boolean; message?: string }> {
    return this.deleteBase(id);
  }

  /**
   * Lấy danh sách quản trị viên đang online
   * @returns Danh sách quản trị viên đang online
   */
  async getOnlineAdmins(): Promise<User[]> {
    return this.getAllBase({ filters: { status: 'online' } });
  }

  /**
   * Tìm kiếm quản trị viên
   * @param query Từ khóa tìm kiếm
   * @param params Tham số phân trang
   * @returns Kết quả tìm kiếm
   */
  async searchAdmins(query: string, params: AdminSearchParams = {}): Promise<PaginatedResult<User>> {
    return this.searchBase(query, params);
  }

  /**
   * Lấy thống kê quản trị viên
   * @returns Thống kê quản trị viên
   */
  async getAdminStats(): Promise<Record<string, any>> {
    return this.getStatsBase();
  }

  /**
   * Cập nhật trạng thái quản trị viên
   * @param id ID của quản trị viên
   * @param status Trạng thái mới
   * @returns Thông tin quản trị viên sau khi cập nhật
   */
  async updateAdminStatus(id: string, status: string): Promise<User> {
    return this.patchBase(id, { status });
  }

  /**
   * Cập nhật vai trò quản trị viên
   * @param id ID của quản trị viên
   * @param role Vai trò mới
   * @returns Thông tin quản trị viên sau khi cập nhật
   */
  async updateAdminRole(id: string, role: string): Promise<User> {
    return this.patchBase(id, { role });
  }
}

// Export instance của service
export const adminService = new AdminService();
