import React from "react";
import {Ava<PERSON>, AvatarFallback} from "@/components/ui/avatar.tsx";
import {Conversation, cskhService} from "@/services/CskhService.ts";
import {Button} from "@/components/ui/button.tsx";
import {MessageCircleX, UserPlus, ArrowLeft} from "lucide-react";
import ButtonResponsive from "@/components/custom/ButtonResponsive";
import ConfirmButton from "@/components/custom/ConfirmButton";
import StatusConvo from "./StatusConvo.tsx";
import {enhancedToast} from "@/components/common/EnhancedToast.tsx";

interface ChatHeaderProps {
  conversation: Conversation;
  backToSidebar?: (room_id: string) => void;
  isSmallScreen?: boolean;
}


const ChatHeader: React.FC<ChatHeaderProps> = ({
                                                 conversation,
                                                 backToSidebar,
                                                 isSmallScreen,
                                               }) => {
  const status = conversation?.status
  const closed = status === 'close'
  const connectedStaff = status === 'waiting' || status === 'open'
  const staff = conversation?.staff || null;
  const title = conversation?.topic?.title || "Hỗ trợ trực tuyến"

  const chatWithStaff = async () => {
    const api = await cskhService.updateStatus(conversation._id, {status: "open"})
    if (api.status === 'waiting')
      enhancedToast.warning("Đang kết nối đến nhân viên");
    if (api.status === 'open')
      enhancedToast.success("Đã kết nối đến nhân viên");
  }

  const closeCurrentConversation = async () => {
    const api = await cskhService.updateStatus(conversation._id, {status: "close"})
    if (api)
      enhancedToast.success("Đã đóng cuộc hội thoại.");
  }

  return (<div
      className="flex justify-between items-center p-4 border-b border-border text-black rounded-t-lg">

      <div className="flex items-center gap-3">
        {isSmallScreen && <Button
            className="flex"
            variant="ghost"
            onClick={() => backToSidebar(conversation._id)}
            size="sm"
        >
            <ArrowLeft size={16}/>
        </Button>}
        <Avatar className="h-8 w-8">
          <AvatarFallback>{title.charAt(0)}</AvatarFallback>
        </Avatar>
        <div>
          <h2 className="text-lg font-semibold">{title}</h2>
          <StatusConvo status={status} staff={staff}/>
        </div>
      </div>

      {!closed && conversation && <div className="flex items-center gap-3">
        {!connectedStaff && <ConfirmButton
            onConfirm={chatWithStaff}
            title="Yêu cầu kết nối nhân viên"
        >
            <ButtonResponsive
                icon={<UserPlus size={20}/>}
                title="Chat với nhân viên"
                responsive={true}
                className="flex items-center gap-1 bg-primary hover:bg-primary/70 p-2 text-white rounded transition-colors"
            />
        </ConfirmButton>}


          <ConfirmButton
              onConfirm={closeCurrentConversation}
              title="Đóng cuộc hội thoại"
          >
              <ButtonResponsive
                  icon={<MessageCircleX size={20}/>}
                  title="Kết thúc"
                  responsive={true}
                  className="flex items-center gap-1 bg-red-500 hover:bg-red-500/70 p-2 text-white rounded transition-colors"
              />
          </ConfirmButton>
      </div>}

    </div>
  )

}

export default ChatHeader;