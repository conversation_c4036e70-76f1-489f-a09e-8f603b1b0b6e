import {BrowserRouter} from "react-router-dom";
import {QueryClient, QueryClientProvider} from "@tanstack/react-query";
import {HelmetProvider} from 'react-helmet-async';
import {ThemeProvider} from "@/components/theme/ThemeProvider";
import {AuthProvider} from "@/contexts/AuthContext";
import {EnhancedToaster} from "@/components/common/EnhancedToast";
import {TooltipProvider} from "@/components/ui/tooltip";
import {AppRoutes} from "@/routes";

const queryClient = new QueryClient();

const App = () => (
  <HelmetProvider>
    <QueryClientProvider client={queryClient}>
      <ThemeProvider defaultTheme="light">
        <BrowserRouter>
          <TooltipProvider>
            <AuthProvider>
              <AppRoutes/>
              <EnhancedToaster/>
            </AuthProvider>
          </TooltipProvider>
        </BrowserRouter>
      </ThemeProvider>
    </QueryClientProvider>
  </HelmetProvider>
);

export default App;
