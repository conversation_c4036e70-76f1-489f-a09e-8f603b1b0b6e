import React from 'react';
import {
    Pagination,
    PaginationContent,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from '@/components/ui/pagination';


interface PaginationBase {
    page: number,
    limit: number,
    totalPage: number;
}

interface PaginationProps {
    pagination: PaginationBase
    onchangePagination:(page: number) => void;
}

const PaginationCustom: React.FC<PaginationProps> =  ({
                              pagination,
                              onchangePagination
                          }) => {




    const renderPaginationItems = () => {
        const items = [];

        for (let i = 1; i <= pagination.totalPage; i++) {
            if (
                i === 1 ||
                i === pagination.totalPage ||
                (i >= pagination.page - 1 && i <= pagination.page + 1)
            ) {
                items.push(
                    <PaginationItem key={i}>
                        <PaginationLink
                            onClick={() => onchangePagination(i)}
                            isActive={pagination.page === i}
                            className="cursor-pointer"
                        >
                            {i}
                        </PaginationLink>
                    </PaginationItem>
                );
            } else if (
                (i === pagination.page - 2 && pagination.page > 3) ||
                (i === pagination.page + 2 && pagination.page < pagination.totalPage - 2)
            ) {
                items.push(
                    <PaginationItem key={i}>
                        <span className="px-4 py-2">...</span>
                    </PaginationItem>
                );
            }
        }

        return items;
    };


    return (<Pagination>
            <PaginationContent>
                <PaginationItem>
                    <PaginationPrevious
                        onClick={() => onchangePagination(Math.max(1, pagination.page - 1))}
                        className={`cursor-pointer ${
                            pagination.page === 1 ? 'pointer-events-none opacity-50' : ''
                        }`}
                    />
                </PaginationItem>
                {renderPaginationItems()}
                <PaginationItem>
                    <PaginationNext
                        onClick={() =>
                            onchangePagination(Math.min(pagination.totalPage, pagination.page + 1))
                        }
                        className={`cursor-pointer ${
                            pagination.page === pagination.totalPage
                                ? 'pointer-events-none opacity-50'
                                : ''
                        }`}
                    />
                </PaginationItem>
            </PaginationContent>
        </Pagination>
    );
};

export {
    PaginationCustom,
    PaginationBase,
}