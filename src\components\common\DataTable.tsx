import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Loader2 } from "lucide-react";
import { Pagination, PaginationInfo } from "./Pagination";

export interface Column<T> {
  /**
   * Tiêu đề cột
   */
  header: React.ReactNode;
  
  /**
   * Hàm truy cập dữ liệu hoặc key của dữ liệu
   */
  accessorKey?: keyof T;
  
  /**
   * Hàm tùy chỉnh để render cell
   */
  cell?: (item: T, index: number) => React.ReactNode;
  
  /**
   * CSS class cho cột
   */
  className?: string;
}

export interface DataTableProps<T> {
  /**
   * Dữ liệu hiển thị trong bảng
   */
  data: T[];
  
  /**
   * Định nghĩa các cột
   */
  columns: Column<T>[];
  
  /**
   * Trạng thái đang tải dữ liệu
   */
  loading?: boolean;
  
  /**
   * Thông tin phân trang
   */
  paginationInfo?: PaginationInfo;
  
  /**
   * Callback khi thay đổi trang
   */
  onPageChange?: (page: number) => void;
  
  /**
   * Tên của các mục đang hiển thị (ví dụ: "quản trị viên", "sản phẩm", v.v.)
   */
  itemName?: string;
  
  /**
   * Thông báo khi không có dữ liệu
   */
  emptyMessage?: string;
  
  /**
   * Hiển thị phân trang
   * @default true
   */
  showPagination?: boolean;
  
  /**
   * Hiển thị nút nhảy đến trang đầu/cuối
   * @default false
   */
  showFirstLastButtons?: boolean;
  
  /**
   * CSS class cho bảng
   */
  className?: string;
  
  /**
   * Hàm xử lý khi click vào hàng
   */
  onRowClick?: (item: T, index: number) => void;
}

export function DataTable<T>({
  data,
  columns,
  loading = false,
  paginationInfo,
  onPageChange,
  itemName = "mục",
  emptyMessage = "Không có dữ liệu",
  showPagination = true,
  showFirstLastButtons = false,
  className = "",
  onRowClick
}: DataTableProps<T>) {
  // Render nội dung cell
  const renderCell = (item: T, column: Column<T>, index: number) => {
    if (column.cell) {
      return column.cell(item, index);
    }
    
    if (column.accessorKey) {
      return item[column.accessorKey] as React.ReactNode;
    }
    
    return null;
  };
  
  // Xử lý click vào hàng
  const handleRowClick = (item: T, index: number) => {
    if (onRowClick) {
      onRowClick(item, index);
    }
  };
  
  return (
    <div className="space-y-4">
      <div className={`rounded-md border ${className}`}>
        <Table>
          <TableHeader>
            <TableRow>
              {columns.map((column, index) => (
                <TableHead key={index} className={column.className}>
                  {column.header}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={columns.length} className="text-center py-10">
                  <div className="flex justify-center items-center">
                    <Loader2 className="h-6 w-6 animate-spin mr-2"/>
                    <span>Đang tải dữ liệu...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : data && data.length > 0 ? (
              data.map((item, index) => (
                <TableRow 
                  key={index} 
                  onClick={() => handleRowClick(item, index)}
                  className={onRowClick ? "cursor-pointer hover:bg-muted/50" : ""}
                >
                  {columns.map((column, colIndex) => (
                    <TableCell key={colIndex} className={column.className}>
                      {renderCell(item, column, index)}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="text-center py-10">
                  {emptyMessage}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      
      {showPagination && paginationInfo && onPageChange && !loading && data.length > 0 && (
        <Pagination
          paginationInfo={paginationInfo}
          onPageChange={onPageChange}
          itemName={itemName}
          showFirstLastButtons={showFirstLastButtons}
        />
      )}
    </div>
  );
}
