import {useState, useCallback} from 'react';
import {knowledgeService, Knowledge} from '@/services/KnowledgeService.ts'
import {enhancedToast} from "@/components/common/EnhancedToast.tsx";


export const useKnowledge = () => {
  const [documents, setDocuments] = useState<Knowledge[]>([]);

  const addMultipleDocuments =  useCallback(async (files: File[])  => {

    const formData = new FormData();
    files.forEach((file) => {
      formData.append("files", file);
    });

    const api = await knowledgeService.createKnowledge(formData);
    if(api?.data) {
      setDocuments(prev => [...api.data, ...prev]);
      enhancedToast.success(`${api.data.length} văn bản đã được upload thành công`)
    }
  }, []);


  const toggleStatus = useCallback(async (id: string, isActive: boolean) => {
    const api = await knowledgeService.changeActiveKnowledge(id, {isActive})
    if(api.success) {
      setDocuments(prev => {
        return prev.map((doc: Knowledge) => {
          return doc._id === id ? api.data : doc
        })
      })
      enhancedToast.success(`Cập nhật văn bản thành công!`)
    }


  }, []);

  return {
    documents,
    setDocuments,
    addMultipleDocuments,
    toggleStatus,
  };
};