import { api } from '@/lib/api';

/**
 * Interface cho response API cơ bản
 */
export interface ApiResponse<T = any> {
  data: T;
  status: number;
  message?: string;
}

/**
 * Interface cho tham số phân trang
 */
export interface PaginationParams {
  page?: number;
  pageSize?: number;
  limit?: number;
}

/**
 * Interface cho query parameters
 */
export interface QueryParams {
  [key: string]: any;
  query?: object; // Complex query object that will be JSON stringified
  sort?: string;  // Sort parameter like "-createdAt" or "name,createdAt"
  limit?: number;
  searchFields?: string | string[]; // Search fields
  searchValue?: string; // Search value
}

/**
 * Interface cho request config
 */
export interface RequestConfig {
  loading?: boolean;
  toastError?: boolean;
  hideNoti?: boolean;
  params?: any;
  headers?: Record<string, string>;
  responseType?: 'json' | 'blob' | 'text' | 'arraybuffer';
  data?: any;
}

/**
 * Interface cho kết quả phân trang (BaseService format)
 */
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

/**
 * Interface cho response phân trang từ backend
 */
export interface BackendPaginatedResponse<T> {
  page: number;
  pageSize: number;
  rows: T[];
  total: number;
  totalPages: number;
}

/**
 * Utility functions
 */

/**
 * Generate populate parameter string
 */
export function genPopulateParam(populateOpts: string[] = []): string {
  if (populateOpts.length === 0) return '';
  return `populate=${populateOpts.join(',')}`;
}

/**
 * Generate query parameter string with support for complex query object
 */
export function genQueryParam(query: QueryParams = {}): string {
  const params = new URLSearchParams();

  Object.entries(query).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (key === 'query' && typeof value === 'object' && !Array.isArray(value)) {
        // Handle complex query object - encode as JSON
        params.append(key, JSON.stringify(value));
      } else if (key === 'searchFields' && Array.isArray(value)) {
        // Handle searchFields array
        params.append(key, value.join(','));
      } else if (Array.isArray(value)) {
        params.append(key, value.join(','));
      } else {
        params.append(key, value.toString());
      }
    }
  });

  return params.toString();
}

/**
 * Generate search field parameter
 */
export function genSearchFieldParam(searchFields: string[] = [], searchValue?: string): string {
  if (!searchValue || searchFields.length === 0) return '';
  return `searchFields=${searchFields.join(',')}&searchValue=${encodeURIComponent(searchValue)}`;
}

/**
 * Convert snake_case to camelCase
 * Preserves MongoDB _id fields and other fields starting with underscore
 */
export function convertSnakeCaseToCamelCase(obj: object): object {
  if (obj === null || typeof obj !== 'object') return obj;

  if (Array.isArray(obj)) {
    return obj.map(convertSnakeCaseToCamelCase);
  }

  const converted: object = {};
  Object.keys(obj).forEach(key => {
    // Preserve fields starting with underscore (like _id, __v, etc.)
    if (key.startsWith('_')) {
      converted[key] = convertSnakeCaseToCamelCase(obj[key]);
    } else {
      const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
      converted[camelKey] = convertSnakeCaseToCamelCase(obj[key]);
    }
  });

  return converted;
}

/**
 * BaseService - Cung cấp các method CRUD cơ bản cho tất cả service
 */
export class BaseService<T = any> {
  protected endpoint: string;

  constructor(endpoint: string) {
    this.endpoint = endpoint;
  }

  /**
   * Build complete URL with parameters
   */
  protected buildUrl(endpoint: string, params?: string): string {
    return params ? `${endpoint}?${params}` : endpoint;
  }

  /**
   * Create new item
   */
  async create<R = T>(
    apiEndpoint: string,
    data: Partial<T>,
    populateOpts: string[] = [],
    loading: boolean = true,
    toastError: boolean = false,
    params?: any
  ): Promise<R | null> {
    try {
      const config: RequestConfig = { loading, toastError, params };
      const populateParams = genPopulateParam(populateOpts);
      const url = this.buildUrl(apiEndpoint, populateParams);

      const response = await api.post<ApiResponse<R>>(url, data, config);

      if (response && response.data) {
        return convertSnakeCaseToCamelCase(response.data);
      }
      return null;
    } catch (error) {
      if (toastError) {
        console.error('Create error:', error);
      }
      return null;
    }
  }

  /**
   * Get all items with pagination
   */
  async getAll<R = T>(
    apiEndpoint: string,
    query: QueryParams = {},
    populateOpts: string[] = [],
    loading: boolean = true,
    toastError: boolean = false
  ): Promise<R[] | null> {
    try {
      const config: RequestConfig = { loading, toastError };
      const queryParams = genQueryParam(query);
      const populateParams = genPopulateParam(populateOpts);
      const allParams = [queryParams, populateParams].filter(Boolean).join('&');
      const url = this.buildUrl(apiEndpoint, allParams);

      const response = await api.get<ApiResponse<R[]>>(url, config);

      if (response && response.data) {
        return convertSnakeCaseToCamelCase(response.data);
      }
      return null;
    } catch (error) {
      if (toastError) {
        console.error('GetAll error:', error);
      }
      return null;
    }
  }

  /**
   * Get paginated items
   * Backend trả về format: {page, pageSize, rows, total, totalPages}
   * Convert thành PaginatedResponse format: {data: [...], pagination: {...}}
   */
  async getAllPaginate<R = T>(
    apiEndpoint: string,
    query: QueryParams & PaginationParams = {},
    populateOpts: string[] = [],
    loading: boolean = true,
    toastError: boolean = false
  ): Promise<PaginatedResponse<R> | null> {
    try {
      const config: RequestConfig = { loading, toastError };
      const queryParams = genQueryParam(query);
      const populateParams = genPopulateParam(populateOpts);
      const allParams = [queryParams, populateParams].filter(Boolean).join('&');
      const url = this.buildUrl(apiEndpoint, allParams);
      // Backend trả về format: {page, pageSize, rows, total, totalPages}
      const response = await api.get<BackendPaginatedResponse<R>>(url, config);

      if (response && response.rows) {
        // Convert backend format to PaginatedResponse format
        const convertedResponse: PaginatedResponse<R> = {
          data: convertSnakeCaseToCamelCase(response.rows),
          pagination: {
            page: response.page,
            pageSize: response.pageSize,
            total: response.total,
            totalPages: response.totalPages
          }
        };

        return convertedResponse;
      }
      return null;
    } catch (error) {
      if (toastError) {
        console.error('GetAllPaginate error:', error);
      }
      return null;
    }
  }

  /**
   * Get item detail by ID
   */
  async getDetail<R = T>(
    apiEndpoint: string,
    id: string,
    populateOpts: string[] = [],
    loading: boolean = true,
    toastError: boolean = false
  ): Promise<R | null> {
    try {
      const config: RequestConfig = { loading, toastError };
      const populateParams = genPopulateParam(populateOpts);
      const url = this.buildUrl(apiEndpoint.replace(':id', id), populateParams);

      const response = await api.get<ApiResponse<R>>(url, config);

      if (response && response.data) {
        return convertSnakeCaseToCamelCase(response.data);
      }
      return null;
    } catch (error) {
      if (toastError) {
        console.error('GetDetail error:', error);
      }
      return null;
    }
  }

  /**
   * Update item by ID
   */
  async update<R = T>(
    apiEndpoint: string,
    data: Partial<T> & { id: string },
    populateOpts: string[] = [],
    loading: boolean = true,
    toastError: boolean = false
  ): Promise<R | null> {
    try {
      if (!data.id) {
        throw new Error('ID is required for update operation');
      }

      const config: RequestConfig = { loading, toastError };
      const populateParams = genPopulateParam(populateOpts);
      const url = this.buildUrl(apiEndpoint.replace(':id', data.id), populateParams);

      const response = await api.put<ApiResponse<R>>(url, data, config);

      if (response && response.data) {
        return convertSnakeCaseToCamelCase(response.data);
      }
      return null;
    } catch (error) {
      if (toastError) {
        console.error('Update error:', error);
      }
      return null;
    }
  }

  /**
   * Delete item by ID
   */
  async delete(
    apiEndpoint: string,
    id: string,
    loading: boolean = true,
    toastError: boolean = false
  ): Promise<any> {
    try {
      const config: RequestConfig = { loading, toastError };
      const url = apiEndpoint.replace(':id', id);

      const response = await api.delete<ApiResponse<any>>(url, config);

      if (response && response.data) {
        return convertSnakeCaseToCamelCase(response.data);
      }
      return null;
    } catch (error) {
      if (toastError) {
        console.error('Delete error:', error);
      }
      return null;
    }
  }

  /**
   * Search items with filters
   */
  async search<R = T>(
    apiEndpoint: string,
    searchFields: string[] = [],
    searchValue?: string,
    query: QueryParams = {},
    populateOpts: string[] = [],
    loading: boolean = true,
    toastError: boolean = false
  ): Promise<R[] | null> {
    try {
      const config: RequestConfig = { loading, toastError };
      const queryParams = genQueryParam(query);
      const populateParams = genPopulateParam(populateOpts);
      const searchParams = genSearchFieldParam(searchFields, searchValue);
      const allParams = [queryParams, populateParams, searchParams].filter(Boolean).join('&');
      const url = this.buildUrl(apiEndpoint, allParams);

      const response = await api.get<ApiResponse<R[]>>(url, config);

      if (response && response.data) {
        return convertSnakeCaseToCamelCase(response.data);
      }
      return null;
    } catch (error) {
      if (toastError) {
        console.error('Search error:', error);
      }
      return null;
    }
  }

  /**
   * Search items with pagination
   */
  async searchPaginate<R = T>(
    apiEndpoint: string,
    searchFields: string[] = [],
    searchValue?: string,
    query: QueryParams & PaginationParams = {},
    populateOpts: string[] = [],
    loading: boolean = true,
    toastError: boolean = false
  ): Promise<PaginatedResponse<R> | null> {
    try {
      const config: RequestConfig = { loading, toastError };
      const queryParams = genQueryParam(query);
      const populateParams = genPopulateParam(populateOpts);
      const searchParams = genSearchFieldParam(searchFields, searchValue);
      const allParams = [queryParams, populateParams, searchParams].filter(Boolean).join('&');
      const url = this.buildUrl(apiEndpoint, allParams);

      const response = await api.get<BackendPaginatedResponse<R>>(url, config);

      if (response && response.rows) {
        // Convert backend format to PaginatedResponse format
        const convertedResponse: PaginatedResponse<R> = {
          data: convertSnakeCaseToCamelCase(response.rows),
          pagination: {
            page: response.page,
            pageSize: response.pageSize,
            total: response.total,
            totalPages: response.totalPages
          }
        };
        return convertedResponse;
      }
      return null;
    } catch (error) {
      if (toastError) {
        console.error('SearchPaginate error:', error);
      }
      return null;
    }
  }

  /**
   * Bulk operations
   */
  async bulkCreate<R = T>(
    apiEndpoint: string,
    dataArray: Partial<T>[],
    populateOpts: string[] = [],
    loading: boolean = true,
    toastError: boolean = false
  ): Promise<R[] | null> {
    try {
      const config: RequestConfig = { loading, toastError };
      const populateParams = genPopulateParam(populateOpts);
      const url = this.buildUrl(apiEndpoint, populateParams);

      const response = await api.post<ApiResponse<R[]>>(url, { items: dataArray }, config);

      if (response && response.data) {
        return convertSnakeCaseToCamelCase(response.data);
      }
      return null;
    } catch (error) {
      if (toastError) {
        console.error('BulkCreate error:', error);
      }
      return null;
    }
  }

  /**
   * Bulk update
   */
  async bulkUpdate<R = T>(
    apiEndpoint: string,
    dataArray: Partial<T>[],
    populateOpts: string[] = [],
    loading: boolean = true,
    toastError: boolean = false
  ): Promise<R[] | null> {
    try {
      const config: RequestConfig = { loading, toastError };
      const populateParams = genPopulateParam(populateOpts);
      const url = this.buildUrl(apiEndpoint, populateParams);

      const response = await api.put<ApiResponse<R[]>>(url, { items: dataArray }, config);

      if (response && response.data) {
        return convertSnakeCaseToCamelCase(response.data);
      }
      return null;
    } catch (error) {
      if (toastError) {
        console.error('BulkUpdate error:', error);
      }
      return null;
    }
  }

  /**
   * Bulk delete
   */
  async bulkDelete(
    apiEndpoint: string,
    ids: string[],
    loading: boolean = true,
    toastError: boolean = false
  ): Promise<any> {
    try {
      const config: RequestConfig = { loading, toastError };

      const response = await api.delete<ApiResponse<any>>(apiEndpoint, {
        ...config,
        data: { ids }
      });

      if (response && response.data) {
        return convertSnakeCaseToCamelCase(response.data);
      }
      return null;
    } catch (error) {
      if (toastError) {
        console.error('BulkDelete error:', error);
      }
      return null;
    }
  }

  /**
   * Utility methods
   */
  async count<R = number>(
    apiEndpoint: string,
    query: QueryParams = {},
    loading: boolean = true,
    toastError: boolean = false
  ): Promise<R | null> {
    try {
      const config: RequestConfig = { loading, toastError };
      const queryParams = genQueryParam(query);
      const url = this.buildUrl(apiEndpoint, queryParams);

      const response = await api.get<ApiResponse<R>>(url, config);

      if (response && response.data) {
        return convertSnakeCaseToCamelCase(response.data);
      }
      return null;
    } catch (error) {
      if (toastError) {
        console.error('Count error:', error);
      }
      return null;
    }
  }

  /**
   * Check if item exists
   */
  async exists(
    apiEndpoint: string,
    id: string,
    loading: boolean = false,
    toastError: boolean = false
  ): Promise<boolean> {
    try {
      const result = await this.getDetail(apiEndpoint, id, [], loading, toastError);
      return result !== null;
    } catch (error) {
      return false;
    }
  }

  /**
   * Upload file
   */
  async uploadFile<R = any>(
    apiEndpoint: string,
    file: File,
    additionalData?: Record<string, any>,
    loading: boolean = true,
    toastError: boolean = false
  ): Promise<R | null> {
    try {
      const config: RequestConfig = {
        loading,
        toastError,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      };

      const formData = new FormData();
      formData.append('file', file);

      if (additionalData) {
        Object.entries(additionalData).forEach(([key, value]) => {
          formData.append(key, typeof value === 'string' ? value : JSON.stringify(value));
        });
      }

      const response = await api.post<ApiResponse<R>>(apiEndpoint, formData, config);

      if (response && response.data) {
        return convertSnakeCaseToCamelCase(response.data);
      }
      return null;
    } catch (error) {
      if (toastError) {
        console.error('Upload error:', error);
      }
      return null;
    }
  }

  /**
   * Download file
   */
  async downloadFile(
    apiEndpoint: string,
    filename?: string,
    loading: boolean = true,
    toastError: boolean = false
  ): Promise<Blob | null> {
    try {
      const config: RequestConfig = {
        loading,
        toastError,
        responseType: 'blob'
      };

      const response = await api.get<Blob>(apiEndpoint, config);

      // Auto download if filename provided
      if (filename && response) {
        const url = window.URL.createObjectURL(response);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }

      return response;
    } catch (error) {
      if (toastError) {
        console.error('Download error:', error);
      }
      return null;
    }
  }
}
