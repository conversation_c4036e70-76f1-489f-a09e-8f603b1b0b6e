.p-quote-link {
    background-color: #3b82f6;
    border-radius: 4px;
    padding: 2px 6px;
    margin: 0 2px;
    text-decoration: none;
    font-size: 0.75rem;
    font-weight: 600;
    display: inline-block;
}

.p-quote-link:hover {
    background-color: #2563eb;
}

.p-no-quote-link {
    color: #3b82f6;
    text-decoration: underline;
}

.p-no-quote-link:hover {
    color: #2563eb;
}

.markdown {
    line-height: 1.6;
}

.markdown pre {
    background-color: #0d1117;
    border-radius: 8px;
    padding: 16px;
    overflow-x: auto;
    margin: 0;
}

.markdown code:not(pre code) {
    background-color: #f6f8fa;
    border-radius: 4px;
    padding: 2px 4px;
    font-size: 0.875em;
    color: #d73a49;
}

.markdown table {
    border-collapse: collapse;
    width: 100%;
    margin: 16px 0;
}

.markdown table th,
.markdown table td {
    border: 1px solid #d0d7de;
    padding: 8px 12px;
    text-align: left;
}

.markdown table th {
    background-color: #f6f8fa;
    font-weight: 600;
}

.markdown table tr:nth-child(even) {
    background-color: #f6f8fa;
}
