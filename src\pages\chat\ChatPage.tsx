import React, {useState, useEffect} from 'react';
import {useAuth} from '@/contexts/AuthContext';
import {chatService, Conversation, ChatMessage, MessageAttachment} from '@/services/ChatService';
import {
  initializeSocket,
  connectSocket,
  getSocket,
  isSocketConnected,
  joinConversation,
  leaveConversation,
  sendMessage
} from '@/services/socket';
import {ChatSidebar} from './components/ChatSidebar';
import {ChatHeader} from './components/ChatHeader';
import {ChatContent} from './components/ChatContent';
import {ChatInput} from './components/ChatInput';
import {toast} from '@/hooks/use-toast';

export const ChatPage: React.FC = () => {
  const {user} = useAuth();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversationId, setActiveConversationId] = useState<string | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [messagesLoading, setMessagesLoading] = useState(false);
  const [socketConnected, setSocketConnected] = useState(false);

  // Initialize socket connection
  useEffect(() => {
    const initSocket = async () => {
      try {
        const socket = initializeSocket();

        // Try to connect
        await connectSocket();
        setSocketConnected(true);

        // Listen for new messages
        socket.on('newMessage', (message: ChatMessage) => {
          console.log('📨 Received newMessage event:', message);

          if (message.conversationId === activeConversationId) {
            // Kiểm tra xem tin nhắn đã tồn tại chưa để tránh duplicate
            setMessages(prev => {
              const exists = prev.some(msg =>
                msg._id === message._id ||
                (msg.content === message.content &&
                 msg.senderId === message.senderId &&
                 Math.abs(new Date(msg.sentAt).getTime() - new Date(message.sentAt).getTime()) < 1000)
              );

              if (exists) {
                console.log('⚠️ Message already exists, skipping duplicate');
                return prev;
              }

              console.log('✅ Adding new message to state');
              return [...prev, message];
            });
          }

          // Update conversation list with new last message
          setConversations(prev =>
            prev?.map(conv =>
              conv._id === message.conversationId
                ? {
                  ...conv,
                  lastMessage: message,
                  unreadCount: message.conversationId === activeConversationId ? 0 : (conv.unreadCount || 0) + 1,
                  updatedAt: message.sentAt
                }
                : conv
            ) || []
          );
        });

        // Listen for message read status
        socket.on('messageRead', ({conversationId, messageId}: { conversationId: string; messageId: string }) => {
          if (conversationId === activeConversationId) {
            setMessages(prev =>
              prev.map(msg =>
                msg._id === messageId
                  ? {...msg, read: true, readAt: new Date().toISOString()}
                  : msg
              )
            );
          }
        });

        socket.on('disconnect', () => {
          setSocketConnected(false);
        });

        socket.on('connect', () => {
          setSocketConnected(true);
        });

      } catch (error) {
        console.warn('Socket connection failed:', error);
        setSocketConnected(false);
        toast({
          title: "Cảnh báo",
          description: "Không thể kết nối socket. Tính năng real-time sẽ không hoạt động.",
          variant: "destructive",
        });
      }
    };

    initSocket();

    return () => {
      const socket = getSocket();
      if (socket) {
        socket.off('newMessage');
        socket.off('messageRead');
        socket.off('disconnect');
        socket.off('connect');
      }
    };
  }, [activeConversationId]);

  // Fetch conversations on component mount
  useEffect(() => {
    fetchConversations();
  }, []);

  // Handle search
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      fetchConversations();
    }, 300);

    return () => clearTimeout(delayedSearch);
  }, [searchQuery]);

  const fetchConversations = async () => {
    try {
      setLoading(true);
      const params = {
        page: 1,
        pageSize: 50,
        populate: ['customerId', 'supportId'],
        query: {
          status: 'open'
        },
      };
      const result = await chatService.getConversations(params);
      console.log('Conversations data:', result.rows); // Debug log

      // Làm sạch dữ liệu conversation để tránh hiển thị unreadCount = 0
      const cleanedConversations = result.rows.map(conv => ({
        ...conv,
        unreadCount: conv.unreadCount && conv.unreadCount > 0 ? conv.unreadCount : undefined
      }));

      setConversations(cleanedConversations);
    } catch (error) {
      console.error('Error fetching conversations:', error);
      toast({
        title: "Lỗi",
        description: "Không thể tải danh sách cuộc trò chuyện",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSelectConversation = async (conversationId: string) => {
    if (activeConversationId === conversationId) return;

    try {
      setMessagesLoading(true);

      // Leave previous conversation room
      if (activeConversationId) {
        leaveConversation(activeConversationId);
      }

      // Join new conversation room
      joinConversation(conversationId);
      setActiveConversationId(conversationId);
      // Fetch messages for the selected conversation using new endpoint
      const result = await chatService.getMessages({
        query: {
          conversationId
        },
        populate: ['senderId'],
        page: 1,
        pageSize: 100
      });
      // getMessagesByConversationId returns array of messages directly
      setMessages(result.rows);

      // Mark conversation as read
      // await chatService.markConversationAsRead(conversationId);

      // Update unread count in conversations list
      setConversations(prev =>
        prev?.map(conv =>
          conv._id === conversationId
            ? {...conv, unreadCount: 0}
            : conv
        ) || []
      );

    } catch (error) {
      console.error('Error selecting conversation:', error);
      toast({
        title: "Lỗi",
        description: "Không thể tải tin nhắn",
        variant: "destructive",
      });
    } finally {
      setMessagesLoading(false);
    }
  };

  const handleSendMessage = async (content: string, attachments: MessageAttachment[] = []) => {
    if (!activeConversationId || !user) return;

    console.log('🚀 Sending message:', { content, socketConnected, isSocketConnected: isSocketConnected() });

    try {
      // Ưu tiên gửi qua socket nếu có kết nối
      if (socketConnected && isSocketConnected()) {
        console.log('📡 Sending via socket only...');

        // Gửi tin nhắn qua socket (backend sẽ lưu vào DB và emit newMessage event)
        sendMessage({
          conversationId: activeConversationId,
          senderId: user._id,
          content,
          attachments, // Thêm attachments vào socket message
        });

        console.log('✅ Message sent via socket - waiting for newMessage event');
      } else {
        // Fallback: Gửi qua API nếu socket không kết nối
        console.log('⚠️ Socket not connected, falling back to API');
        const sentMessage = await chatService.sendMessage(activeConversationId, content, attachments);

        // Thêm tin nhắn vào local state ngay lập tức (chỉ khi không có socket)
        if (sentMessage) {
          console.log('✅ Message sent via API, adding to local state');
          setMessages(prev => [...prev, sentMessage]);

          // Cập nhật conversation list
          setConversations(prev =>
            prev?.map(conv =>
              conv._id === activeConversationId
                ? {
                  ...conv,
                  lastMessage: sentMessage,
                  updatedAt: sentMessage.sentAt
                }
                : conv
            ) || []
          );
        }
      }

    } catch (error) {
      console.error('❌ Error sending message:', error);
      toast({
        title: "Lỗi",
        description: "Không thể gửi tin nhắn",
        variant: "destructive",
      });
    }
  };

  const activeConversation = conversations?.find(conv => conv._id === activeConversationId);

  return (
    <div className="h-[calc(100vh-8rem)] flex bg-background rounded-lg border border-border overflow-hidden">
      {/* Sidebar */}
      <ChatSidebar
        conversations={conversations}
        activeConversationId={activeConversationId}
        onSelectConversation={handleSelectConversation}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        loading={loading}
      />

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <ChatHeader
          conversation={activeConversation || null}
          onCall={() => {
            // TODO: Integrate with call center
            toast({
              title: "Tính năng đang phát triển",
              description: "Tính năng gọi điện sẽ được tích hợp với call center",
            });
          }}
          onVideoCall={() => {
            // TODO: Implement video call
            toast({
              title: "Tính năng đang phát triển",
              description: "Tính năng gọi video sẽ được phát triển trong tương lai",
            });
          }}
          onViewProfile={() => {
            // TODO: Navigate to customer profile
            toast({
              title: "Tính năng đang phát triển",
              description: "Tính năng xem thông tin khách hàng sẽ được tích hợp",
            });
          }}
        />

        {/* Messages */}
        <ChatContent
          messages={messages}
          currentUserId={user?._id || ''}
          conversation={activeConversation || null}
          loading={messagesLoading}
        />

        {/* Input */}
        {activeConversationId && (
          <ChatInput
            onSendMessage={handleSendMessage}
            disabled={!activeConversationId}
            placeholder={socketConnected ? "Nhập tin nhắn..." : "Nhập tin nhắn (offline)..."}
          />
        )}

        {/* Socket status indicator */}
        {!socketConnected && (
          <div className="px-4 py-2 bg-yellow-50 border-t border-yellow-200 text-yellow-800 text-sm">
            ⚠️ Chế độ offline - Tin nhắn sẽ được gửi nhưng không có real-time updates
          </div>
        )}
      </div>
    </div>
  );
};
