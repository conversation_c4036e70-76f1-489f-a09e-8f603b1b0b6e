import React, {useState, useEffect} from 'react';
import {
  ArrowRight,
  Search,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import {Button} from '@/components/ui/button';
import {Card, CardDescription, CardHeader, CardTitle} from '@/components/ui/card';
import {Input} from '@/components/ui/input';
import {topicService, Topic} from '@/services/TopicService.ts'
import {cskhService} from '@/services/CskhService.ts'
import {enhancedToast} from "@/components/common/EnhancedToast.tsx";
import {useAuth} from "@/contexts/AuthContext.tsx";


const TopicChat = () => {
  const {user} = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [topics, setTopics] = useState<Topic[]>([]);
  const [showAllTopics, setShowAllTopics] = useState(false);

  useEffect(() => {
    getTopics().then()
  }, []);

  const getTopics = async () => {
    const api = await topicService.getAllTopic({pagination: false})
    if (api) setTopics(api.data);
  }


  const handleTopicSelect = async (topicId: string) => {
    const api = await cskhService.createConversations({topicId, userId: user._id})
    if (api) enhancedToast.success("Đã mở cuộc trò chuyện mới!")
  };


  const filteredTopics = topics.filter(topic =>
    topic.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    topic.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const showTotal = 6
  const displayedTopics = showAllTopics ? filteredTopics : filteredTopics.slice(0, showTotal);

  return (
    <div className="flex-1 bg-transparent h-full p-4">
      <div className="mx-auto md:py-8">
        {/* Header */}
        <div className="text-center mb-6 md:mb-8">
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
            Chăm sóc khách hàng
          </h1>
          <p className="text-gray-600 text-base md:text-lg px-4">
            Chọn chủ đề bạn cần tư vấn để bắt đầu cuộc trò chuyện
          </p>
        </div>

        {/* Search */}
        <div className="max-w-md mx-auto mb-6 md:mb-8">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"/>
            <Input
              type="text"
              placeholder="Tìm kiếm chủ đề..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-white"
            />
          </div>
        </div>

        {/* Topic Selection */}
        <div className="max-w-6xl mx-auto mb-6 md:mb-8">
          {filteredTopics.length > 0 ? (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                {displayedTopics.map((topic) => {
                  return (
                    <Card
                      key={topic._id}
                      className="hover:shadow-lg transition-all duration-300 cursor-pointer border-2 hover:border-primary/80 group"
                      onClick={() => handleTopicSelect(topic._id)}
                    >
                      <CardHeader className="pb-2 md:pb-3">
                        <div className="flex items-start space-x-3">
                          {/*<div className={`${topic.color} p-2 md:p-3 rounded-lg flex-shrink-0`}>*/}
                          {/*  <IconComponent className="h-4 w-4 md:h-6 md:w-6 text-white" />*/}
                          {/*</div>*/}
                          <div className="flex-1 min-w-0">
                            <CardTitle className="text-sm md:text-lg leading-tight mb-1">
                              {topic.title}
                            </CardTitle>
                            <CardDescription className="text-xs md:text-sm leading-tight">
                              {topic.description}
                            </CardDescription>
                          </div>
                        </div>
                        <div className="flex justify-end mt-2">
                          <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-blue-500 transition-colors"/>
                        </div>
                      </CardHeader>
                    </Card>
                  );
                })}
              </div>

              {/* Show More/Less Button */}
              {!searchTerm && filteredTopics.length > showTotal && (
                <div className="text-center mt-6">
                  <Button
                    variant="outline"
                    onClick={() => setShowAllTopics(!showAllTopics)}
                    className="bg-white hover:bg-gray-50"
                  >
                    {showAllTopics ? (
                      <>
                        <ChevronUp className="mr-2 h-4 w-4"/>
                        Thu gọn
                      </>
                    ) : (
                      <>
                        <ChevronDown className="mr-2 h-4 w-4"/>
                        Xem thêm chủ đề ({filteredTopics.length - showTotal} chủ đề khác)
                      </>
                    )}
                  </Button>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-8">
              <div className="text-gray-500 mb-2">
                <Search className="h-12 w-12 mx-auto opacity-50"/>
              </div>
              <p className="text-gray-500">Không tìm thấy chủ đề phù hợp</p>
              <p className="text-sm text-gray-400 mt-1">Thử tìm kiếm với từ khóa khác</p>
            </div>
          )}
        </div>

      </div>
    </div>
  );
};

export default TopicChat;