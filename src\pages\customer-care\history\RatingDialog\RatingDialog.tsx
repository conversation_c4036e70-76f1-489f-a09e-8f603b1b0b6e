import React, {useState} from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import {Button} from '@/components/ui/button';
import {cskhService} from "@/services/CskhService.ts"
import {Star} from 'lucide-react';
import {Label} from '@/components/ui/label';
import {enhancedToast} from "@/components/common/EnhancedToast.tsx";

interface RatingDialogProps {
  isOpen: boolean;
  onClose: () => void;
  fetchConversations: () => void;
  roomId: string;
}

const RatingDialog = ({isOpen, onClose, roomId, fetchConversations}: RatingDialogProps) => {
  const [rating, setRating] = useState(0);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [comment, setComment] = useState('');

  const handleSubmit = async () => {
    if (rating === 0) {
      enhancedToast.error("Vui lòng chọn điểm đánh giá.");
      return;
    }

    if (rating < 4 && comment.trim() === '') {
      enhancedToast.error("Vui lòng nhập nhận xét khi đánh giá dưới 4 sao.");
      return;
    }

    const api = await cskhService.updateConversation(roomId, {feedback: {rating, comment}})
    if(api) {
      fetchConversations()
      enhancedToast.success("Cảm ơn đánh giá của bạn!.");
    }

    handleClose()
  };

  const handleClose = () => {
    setRating(0);
    setComment('');
    onClose();
  };

  // Check if comment is required based on current rating
  const isCommentRequired = rating > 0 && rating < 4;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Đánh giá dịch vụ hỗ trợ</DialogTitle>
          <DialogDescription>
            Đánh giá chất lượng chăm sóc khách hàng
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Rating Stars */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              Điểm đánh giá <span className="text-red-500">*</span>
            </Label>
            <div className="flex items-center space-x-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  onClick={() => setRating(star)}
                  onMouseEnter={() => setHoveredRating(star)}
                  onMouseLeave={() => setHoveredRating(0)}
                  className="p-1 transition-colors"
                >
                  <Star
                    className={`h-6 w-6 ${
                      star <= (hoveredRating || rating)
                        ? 'fill-yellow-400 text-yellow-400'
                        : 'text-gray-300'
                    } transition-colors`}
                  />
                </button>
              ))}
              {rating > 0 && (
                <span className="ml-2 text-sm text-gray-600">
                  {rating} sao
                </span>
              )}
            </div>
          </div>

          {/* Comment */}
          <div className="space-y-2">
            <Label htmlFor="comment" className="text-sm font-medium">
              Nhận xét {isCommentRequired && <span className="text-red-500">*</span>}
              {!isCommentRequired && <span className="text-gray-500">(Không bắt buộc)</span>}
            </Label>
            <textarea
              id="comment"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder={
                isCommentRequired
                  ? "Vui lòng chia sẻ lý do đánh giá để chúng tôi cải thiện dịch vụ..."
                  : "Chia sẻ trải nghiệm của bạn..."
              }
              className={`w-full min-h-[80px] rounded-md border px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 resize-none ${
                isCommentRequired
                  ? 'border-input bg-background'
                  : 'border-input bg-background'
              }`}
              maxLength={500}
            />
            <div className="flex justify-between items-center">
              <div className="text-xs text-gray-500 text-right">
                {comment.length}/500
              </div>
              {isCommentRequired && (
                <div className="text-xs text-red-500">
                  Bắt buộc nhập nhận xét cho đánh giá dưới 4 sao
                </div>
              )}
            </div>
          </div>
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button variant="outline" onClick={handleClose}>
            Hủy
          </Button>
          <Button onClick={handleSubmit} disabled={rating === 0}>
            Gửi đánh giá
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RatingDialog;