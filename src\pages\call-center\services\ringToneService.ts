/**
 * Service quản lý âm thanh chuông báo cuộc gọi
 * Tạo và phát âm thanh chuông khi có cuộc gọi đến
 */

export class RingToneService {
    private isRinging = false;
    private incomingCallAudio: HTMLAudioElement | null = null; // Âm thanh cho người nghe
    private outgoingCallAudio: HTMLAudioElement | null = null; // Âm thanh cho người gọi

    // Đường dẫn đến file âm thanh
    private readonly INCOMING_RING_URL = '/assets/audio/ring-coming.mp3'; // Chuông cho người nghe
    private readonly OUTGOING_RING_URL = '/assets/audio/ring-wait.mp3';   // Chuông cho người gọi

    constructor() {
        this.initializeAudioElements();
    }

    /**
     * Khởi tạo các Audio Elements
     */
    private initializeAudioElements(): void {
        try {
            // Tạo audio element cho chuông báo cuộc gọi đến (người nghe)
            this.incomingCallAudio = new Audio();
            this.incomingCallAudio.src = this.INCOMING_RING_URL;
            this.incomingCallAudio.loop = true; // Lặp lại liên tục
            this.incomingCallAudio.volume = 0.8; // Âm lượng cao hơn để dễ nghe
            this.incomingCallAudio.preload = 'auto';

            // Tạo audio element cho chuông báo cuộc gọi đi (người gọi)
            this.outgoingCallAudio = new Audio();
            this.outgoingCallAudio.src = this.OUTGOING_RING_URL;
            this.outgoingCallAudio.loop = true; // Lặp lại liên tục
            this.outgoingCallAudio.volume = 0.7; // Âm lượng vừa phải
            this.outgoingCallAudio.preload = 'auto';

            // Xử lý lỗi khi không thể phát âm thanh
            this.incomingCallAudio.onerror = () => {
                console.warn('Không thể tải âm thanh chuông đến');
                this.incomingCallAudio = null;
            };

            this.outgoingCallAudio.onerror = () => {
                console.warn('Không thể tải âm thanh chuông chờ');
                this.outgoingCallAudio = null;
            };

        } catch (error) {
            console.error('Không thể khởi tạo Audio Elements:', error);
        }
    }

    /**
     * Bắt đầu phát chuông báo cho cuộc gọi đến (người nghe)
     */
    public startIncomingRing(): void {
        if (this.isRinging) return;

        console.log('🔔 Bắt đầu phát chuông báo cuộc gọi đến');
        this.isRinging = true;

        if (this.incomingCallAudio) {
            this.playIncomingAudioFile();
        }
    }

    /**
     * Bắt đầu phát chuông báo cho cuộc gọi đi (người gọi)
     */
    public startOutgoingRing(): void {
        if (this.isRinging) return;

        console.log('🔔 Bắt đầu phát chuông chờ cuộc gọi đi');
        this.isRinging = true;

        if (this.outgoingCallAudio) {
            this.playOutgoingAudioFile();
        }
    }

    /**
     * Bắt đầu phát chuông báo liên tục (phương thức cũ, giữ để tương thích)
     * Mặc định sử dụng chuông đến
     */
    public startRinging(): void {
        this.startIncomingRing();
    }

    /**
     * Phát file âm thanh chuông báo cuộc gọi đến
     */
    private playIncomingAudioFile(): void {
        if (!this.incomingCallAudio || !this.isRinging) return;

        try {
            this.incomingCallAudio.currentTime = 0;
            this.incomingCallAudio.play().catch(error => {
                console.warn('Không thể phát file âm thanh chuông đến:', error);
            });
        } catch (error) {
            console.warn('Lỗi khi phát file âm thanh chuông đến:', error);
        }
    }

    /**
     * Phát file âm thanh chuông báo cuộc gọi đi
     */
    private playOutgoingAudioFile(): void {
        if (!this.outgoingCallAudio || !this.isRinging) return;

        try {
            this.outgoingCallAudio.currentTime = 0;
            this.outgoingCallAudio.play().catch(error => {
                console.warn('Không thể phát file âm thanh chuông chờ:', error);
            });
        } catch (error) {
            console.warn('Lỗi khi phát file âm thanh chuông chờ:', error);
        }
    }

    /**
     * Dừng phát chuông báo
     */
    public stopRinging(): void {
        if (!this.isRinging) return;

        console.log('🔕 Dừng phát chuông báo cuộc gọi');
        this.isRinging = false;

        // Dừng file âm thanh chuông đến nếu đang phát
        if (this.incomingCallAudio) {
            this.incomingCallAudio.pause();
            this.incomingCallAudio.currentTime = 0;
        }

        // Dừng file âm thanh chuông đi nếu đang phát
        if (this.outgoingCallAudio) {
            this.outgoingCallAudio.pause();
            this.outgoingCallAudio.currentTime = 0;
        }
    }

    /**
     * Kiểm tra trạng thái đang phát chuông
     */
    public get isCurrentlyRinging(): boolean {
        return this.isRinging;
    }

    /**
     * Điều chỉnh âm lượng chuông (0.0 - 1.0)
     */
    public setVolume(volume: number): void {
        const clampedVolume = Math.max(0, Math.min(1, volume));

        // Điều chỉnh âm lượng cho file âm thanh chuông đến
        if (this.incomingCallAudio) {
            this.incomingCallAudio.volume = clampedVolume;
        }

        // Điều chỉnh âm lượng cho file âm thanh chuông đi
        if (this.outgoingCallAudio) {
            this.outgoingCallAudio.volume = clampedVolume * 0.9; // Chuông đi nhỏ hơn một chút
        }
    }

    /**
     * Cleanup khi không sử dụng nữa
     */
    public dispose(): void {
        this.stopRinging();

        // Cleanup audio elements
        if (this.incomingCallAudio) {
            this.incomingCallAudio.pause();
            this.incomingCallAudio.src = '';
            this.incomingCallAudio = null;
        }

        if (this.outgoingCallAudio) {
            this.outgoingCallAudio.pause();
            this.outgoingCallAudio.src = '';
            this.outgoingCallAudio = null;
        }
    }
}

// Export singleton instance
export const ringToneService = new RingToneService();
export default ringToneService;
