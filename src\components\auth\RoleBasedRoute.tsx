import { Navigate, Outlet } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { LoadingScreen } from "../ui/loading-screen";

interface RoleBasedRouteProps {
  allowedRoles: string[];
  redirectTo?: string;
}

export const RoleBasedRoute = ({ allowedRoles, redirectTo = "/dashboard" }: RoleBasedRouteProps) => {
  const { user, loading, isAuthenticated } = useAuth();

  if (loading) {
    return <LoadingScreen />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/auth" replace />;
  }

  // Check if user role is allowed
  if (user && !allowedRoles.includes(user.role)) {
    return <Navigate to={redirectTo} replace />;
  }

  return <Outlet />;
};
