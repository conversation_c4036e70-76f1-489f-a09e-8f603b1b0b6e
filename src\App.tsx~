
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { HelmetProvider } from 'react-helmet-async';
import { ThemeProvider } from "@/components/theme/ThemeProvider";
import { AuthProvider } from "@/contexts/AuthContext";
import { PrivateRoute } from "@/components/auth/PrivateRoute";
import { EnhancedToaster } from "@/components/common/EnhancedToast";

// Auth Pages
import LoginPage from "@/pages/auth/Login";
import AuthPage from "@/pages/auth/Auth";
import ForgotPasswordPage from "@/pages/auth/ForgotPassword";
import ResetPasswordPage from "@/pages/auth/ResetPassword";

// Dashboard Pages
import Dashboard from "@/pages/dashboard";
import CallCenter from "@/pages/call-center/pages/CallCenter";
import Admins from "@/pages/admins";
import Customers from "@/pages/customers";
import ChatCustomer from "@/pages/chat-customer";
import ProfilePage from "@/pages/profile";
import NotFound from "@/pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <HelmetProvider>
    <QueryClientProvider client={queryClient}>
      <ThemeProvider defaultTheme="light">
        <BrowserRouter>
          <AuthProvider>
          <Routes>
            {/* Public routes */}
            <Route path="/login" element={<Navigate to="/auth" replace />} />

            {/* Auth routes with nested structure */}
            <Route path="/auth" element={<AuthPage />}>
              <Route index element={<LoginPage />} />
              <Route path="forgot-password" element={<ForgotPasswordPage />} />
              <Route path="reset-password/:token" element={<ResetPasswordPage />} />
            </Route>

            {/* Legacy routes for backward compatibility */}
            <Route path="/forgot-password" element={<Navigate to="/auth/forgot-password" replace />} />
            <Route path="/reset-password/:token" element={<Navigate to={window.location.pathname.replace('/reset-password/', '/auth/reset-password/')} replace />} />

            {/* Protected routes */}
            <Route element={<PrivateRoute />}>
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/call-center" element={<CallCenter />} />

              <Route path="/admins" element={<Admins />} />

              <Route path="/customers" element={<Customers />} />
              <Route path="/chat-customer" element={<ChatCustomer />} />
              <Route path="/profile" element={<ProfilePage />} />
            </Route>

            {/* Redirect root to dashboard or login based on auth status */}
            <Route path="/" element={<Navigate to="/dashboard" replace />} />

            {/* 404 route */}
            <Route path="*" element={<NotFound />} />
          </Routes>

          <EnhancedToaster />
        </AuthProvider>
        </BrowserRouter>
      </ThemeProvider>
    </QueryClientProvider>
  </HelmetProvider>
);

export default App;
