import React from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { MoreHorizontal } from "lucide-react";
import { cn } from "@/lib/utils";

export interface ActionItem {
  /**
   * Label của action
   */
  label: string;
  
  /**
   * Icon của action
   */
  icon?: React.ReactNode;
  
  /**
   * Callback khi click vào action
   */
  onClick: () => void;
  
  /**
   * CSS class bổ sung
   */
  className?: string;
  
  /**
   * Có hiển thị action không
   * @default true
   */
  show?: boolean;
  
  /**
   * Có disable action không
   * @default false
   */
  disabled?: boolean;
  
  /**
   * Variant của action
   * @default "default"
   */
  variant?: "default" | "destructive";
}

interface ActionMenuProps {
  /**
   * Danh sách các action
   */
  actions: ActionItem[];
  
  /**
   * Label của menu
   */
  label?: string;
  
  /**
   * Nút trigger tùy chỉnh
   */
  triggerButton?: React.ReactNode;
  
  /**
   * CSS class bổ sung cho trigger
   */
  triggerClassName?: string;
  
  /**
   * Vị trí của menu
   * @default "bottom-end"
   */
  align?: "start" | "center" | "end";
  
  /**
   * Có hiển thị icon mặc định không
   * @default true
   */
  showIcon?: boolean;
}

export function ActionMenu({
  actions,
  label,
  triggerButton,
  triggerClassName,
  align = "end",
  showIcon = true
}: ActionMenuProps) {
  // Lọc các action có show = true hoặc không có thuộc tính show
  const visibleActions = actions.filter(action => action.show !== false);
  
  // Nếu không có action nào hiển thị, không render component
  if (visibleActions.length === 0) {
    return null;
  }
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        {triggerButton ? (
          triggerButton
        ) : (
          <Button 
            variant="ghost" 
            size="icon"
            className={cn("h-8 w-8", triggerClassName)}
          >
            <MoreHorizontal className="h-4 w-4" />
            <span className="sr-only">Mở menu</span>
          </Button>
        )}
      </DropdownMenuTrigger>
      <DropdownMenuContent align={align} className="w-48">
        {label && (
          <>
            <DropdownMenuLabel>{label}</DropdownMenuLabel>
            <DropdownMenuSeparator />
          </>
        )}
        
        {visibleActions.map((action, index) => (
          <DropdownMenuItem
            key={index}
            onClick={action.onClick}
            disabled={action.disabled}
            className={cn(
              action.variant === "destructive" && "text-destructive focus:text-destructive",
              action.className
            )}
          >
            {showIcon && action.icon && (
              <span className="mr-2">{action.icon}</span>
            )}
            {action.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
