import React, {useState, useEffect, useCallback} from 'react';
import {cskhService, Message, Conversation} from '@/services/CskhService';
import ChatHeader from '@/pages/customer-care/customer/ChatHeader'
import ChatContent from '@/pages/customer-care/components/ChatContent.tsx'
import ChatInputV2 from '@/pages/customer-care/components/ChatInputV2.tsx'
import ChatSidebar from '@/pages/customer-care/customer/ChatSidebar'
import {useAuth} from "@/contexts/AuthContext.tsx";
import useSocket from "@/hooks/use-socket.ts";
import {useIsMobile} from "@/hooks/use-mobile"
import {SEO} from "@/components/SEO.tsx";
import {CustomerLayout} from "@/components/layout/CustomerLayout.tsx";
import {enhancedToast} from "@/components/common/EnhancedToast.tsx";
import {useDebouncedSearch} from "@/hooks/use-debounced-search.ts";

export default function ChatCustomer() {
  const {user} = useAuth();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversationId, setActiveConversationId] = useState<string>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showNewConvo, setShowNewConvo] = useState(false);
  const [notYetRead, setNotYetRead] = useState(false);
  const [messagesLoading, setMessagesLoading] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const {socket, socketConnected} = useSocket(user._id);
  const isSmallScreen = useIsMobile(1024)


  useEffect(() => {
    fetchConversations().then()
  }, []);


  const fetchConversations = async () => {
    try {
      const api = await cskhService.getCustomerConversations({
        pagination: false,
        title: searchQuery,
      });
      if (api)
        setConversations(api.data)
    } catch (error) {
      enhancedToast.error("Đã có lỗi xảy ra!")
    }
  }


  const addMessageStream = (event, message) => {
    setMessages(prevState => {
      let updatedMessages = [...prevState];
      const lastIndex = updatedMessages.length - 1

      if (event === 'start')
        updatedMessages = [...updatedMessages, message]
      if (event === 'typing' && updatedMessages[lastIndex]?._id === message._id)
        updatedMessages[lastIndex] = message
      if (event === 'end') {
        if (updatedMessages[lastIndex]._id === message._id)
          updatedMessages[lastIndex] = message
        else
          updatedMessages = [...updatedMessages, message]
      }

      return updatedMessages
    })
  }

  useEffect(() => {
    if (!socket) return;

    socket.on("new_message", (data) => {
      setMessages(prevState => {
        return [...prevState, data.message];
      })
    });

    socket.on("notify_conversation", (data) => {
      if (data.isCreated) {
        setConversations(prevState => {
          return [data.room, ...prevState];
        })
        setShowNewConvo(false)
        setActiveConversationId(data.room._id)
      } else {
        setConversations(prevState => {
          return [data.room, ...prevState.filter(e => e._id !== data.room._id)]
        })
      }


    });

    socket.on("update_conversation", (data) => {
      const {room} = data
      const isOpen = room.status === 'open' || room.status === 'chatbot' || room.status === 'waiting';
      if (!isOpen) {
        setConversations(prevState => {
          return prevState.filter(e => e._id !== data.room._id)
        })
        setMessages([])
        setActiveConversationId(null)
      } else {
        setConversations(prevState =>
          prevState.map(item =>
            item._id === data.room._id ? {...item, ...data.room} : item
          )
        );
      }
    });

    socket.on("read_message", (data) => {
      setConversations(prevState => {
        return prevState.map(item =>
          item._id === data.room._id ? data.room : item
        );
      });
      setMessages(data.messages);
    });

    socket.on("bot_streaming", (data) => {
      addMessageStream(data.event, data.message)
    });

    return () => {
      socket.off()
    };

  }, [socket]);


  useEffect(() => {
    const run = async () => {
      if (activeConversationId) {
        socket.emit("join_room", {room_id: activeConversationId});
        await cskhService.markReadMessage(activeConversationId);
      }
    };

    run().then();
  }, [activeConversationId]);


  const serchConversations = useCallback(() => {
    fetchConversations()
  }, [searchQuery]);

  useDebouncedSearch(searchQuery, 500, serchConversations);


  const handleSendMessage = async (conversation, body) => {
    if (!user) return;
    try {
      await cskhService.sendMessages(conversation._id, body)
    } catch (error) {
      enhancedToast.error("Không thể gửi tin nhắn!")
    }
  }


  const handleSelectConversation = async (conversation: Conversation) => {
    if (activeConversationId === conversation._id) {
      await cskhService.markReadMessage(activeConversationId);
    } else {
      if (activeConversationId) socket.emit("leave_room", {room_id: activeConversationId});
      setActiveConversationId(conversation._id);
      setShowNewConvo(false)
    }
  }

  const backToSidebar = (room_id?: string) => {
    if (room_id) {
      socket.emit("leave_room", {room_id: room_id});
      setActiveConversationId(null)
    }
    setShowNewConvo(false)
  }


  const handleCreateConversation = () => {
    setShowNewConvo(true)
    setMessages([])
    setActiveConversationId(null)
  }

  const activeConversation = conversations?.find(conv => conv._id === activeConversationId);
  const showSidebar = !isSmallScreen || (isSmallScreen && !(activeConversationId || showNewConvo))
  const showContent = ((isSmallScreen && (activeConversationId !== null || showNewConvo)) || !isSmallScreen)


  return (
    <CustomerLayout>
      <SEO
        title={`Chat với CSKH`}
        description="Trò chuyện trực tiếp với nhân viên chăm sóc khách hàng"
      />

      <div className="space-y-6">

        {/* Chat Interface */}
        <div className="h-[calc(100vh-8rem)] flex bg-background rounded-lg border border-border overflow-hidden">

          {showSidebar && <ChatSidebar
              isSmallScreen={isSmallScreen}
              conversations={conversations}
              activeConversationId={activeConversationId}
              onSelectConversation={handleSelectConversation}
              searchQuery={searchQuery}
              onSearchChange={setSearchQuery}
              handleCreateConversation={handleCreateConversation}
          />}

          {showContent && <div className="flex-1 flex flex-col">
            {activeConversationId && <ChatHeader
                conversation={activeConversation}
                backToSidebar={backToSidebar}
                isSmallScreen={isSmallScreen}
            />}

              <ChatContent
                  isSmallScreen={isSmallScreen}
                  backToSidebar={backToSidebar}
                  messages={messages}
                  conversation={activeConversation}
                  showAvatar={true}
                  messagesLoading={messagesLoading}
                  showNewConvo={showNewConvo}
                  setNotYetRead={setNotYetRead}
              />

            {activeConversationId && <ChatInputV2
                notYetRead={notYetRead}
                conversation={activeConversation}
                handleSendMessage={handleSendMessage}
                disabled={activeConversation?.status === 'close' || !activeConversationId}
            />}
          </div>}
        </div>
      </div>

    </CustomerLayout>

  );
}
