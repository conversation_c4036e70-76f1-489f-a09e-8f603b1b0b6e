import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";

interface SearchBarProps {
  /**
   * Giá trị hiện tại của ô tìm kiếm
   */
  value: string;
  
  /**
   * Callback khi giá trị thay đổi
   */
  onChange: (value: string) => void;
  
  /**
   * Placeholder cho ô tìm kiếm
   * @default "Tìm kiếm..."
   */
  placeholder?: string;
  
  /**
   * C<PERSON> sử dụng debounce không
   * @default true
   */
  useDebounce?: boolean;
  
  /**
   * Thời gian debounce (ms)
   * @default 300
   */
  debounceTime?: number;
  
  /**
   * Có hiển thị nút xóa không
   * @default true
   */
  showClearButton?: boolean;
  
  /**
   * CSS class bổ sung
   */
  className?: string;
  
  /**
   * <PERSON><PERSON> hiển thị nút tìm kiếm không
   * @default false
   */
  showSearchButton?: boolean;
  
  /**
   * Text của nút tìm kiếm
   * @default "Tìm kiếm"
   */
  searchButtonText?: string;
}

export function SearchBar({
  value,
  onChange,
  placeholder = "Tìm kiếm...",
  useDebounce = true,
  debounceTime = 300,
  showClearButton = true,
  className = "",
  showSearchButton = false,
  searchButtonText = "Tìm kiếm"
}: SearchBarProps) {
  const [inputValue, setInputValue] = useState(value);
  
  // Xử lý debounce
  useEffect(() => {
    if (!useDebounce) {
      return;
    }
    
    const handler = setTimeout(() => {
      onChange(inputValue);
    }, debounceTime);
    
    return () => {
      clearTimeout(handler);
    };
  }, [inputValue, onChange, useDebounce, debounceTime]);
  
  // Cập nhật inputValue khi value thay đổi từ bên ngoài
  useEffect(() => {
    setInputValue(value);
  }, [value]);
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    
    if (!useDebounce) {
      onChange(newValue);
    }
  };
  
  const handleClear = () => {
    setInputValue("");
    onChange("");
  };
  
  const handleSearch = () => {
    onChange(inputValue);
  };
  
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div className="relative flex-1">
        <Search className="absolute left-2.5 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"/>
        <Input
          type="search"
          placeholder={placeholder}
          className="pl-8"
          value={inputValue}
          onChange={handleChange}
        />
        {showClearButton && inputValue && (
          <button
            type="button"
            className="absolute right-2.5 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
            onClick={handleClear}
            aria-label="Xóa tìm kiếm"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        )}
      </div>
      
      {showSearchButton && (
        <Button type="button" onClick={handleSearch}>
          {searchButtonText}
        </Button>
      )}
    </div>
  );
}
