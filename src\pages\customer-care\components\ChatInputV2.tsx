import React, {useState, useRef} from "react";
import {Files, FileImage, ShoppingBag, Send} from "lucide-react";
import {cn} from '@/lib/utils';
import {Conversation, cskhService, bodyMessages} from '@/services/CskhService'

interface ChatInputProps {
  handleSendMessage: (convo: Conversation, body: bodyMessages) => void;
  disabled?: boolean;
  notYetRead: boolean;
  conversation: Conversation;
  placeholder?: string;
}

const ChatInput: React.FC<ChatInputProps> = ({
                                               disabled = false,
                                               notYetRead = false,
                                               conversation,
                                               handleSendMessage,
                                               placeholder = "Nhấn Enter để gửi, Shift + Enter để xuống dòng",
                                             }) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [message, setMessage] = useState('');

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onClickSend()
    }
  };

  const handleFocus = async () => {
    if (conversation.unread > 0)
      await cskhService.markReadMessage(conversation._id);
  }

  const onClickSend = async () => {
    const trimmedMessage = message.trim();
    if (trimmedMessage && !disabled) {
      handleSendMessage(conversation, {text: trimmedMessage})
      setMessage('')
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    }
  }


  const handleInputChange = async (value: string) => {
    if (notYetRead)
      await cskhService.markReadMessage(conversation._id);

    setMessage(value);

    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
    }
  };


  return (
    <div className="flex flex-col mb-2 mx-4">
      <div className="w-full bg-white border border-solid border-gray-300 px-4 pt-2
        !font-sans rounded-3xl shadow-sm hover:shadow-md transition-shadow">

          <textarea
            ref={textareaRef}
            value={message}
            disabled={disabled}
            onChange={(e) => handleInputChange(e.target.value)}
            onKeyPress={handleKeyPress}
            onFocus={handleFocus}
            placeholder={placeholder}
            className={cn("flex-1 px-3 py-2 bg-transparent border-0 rounded-lg w-full",
              "focus:outline-none focus:border-0 focus:border-purple-500 ",
              "text-base placeholder:text-base resize-none max-h-20 text-slate-800",
              "placeholder-slate-400 overflow-y-auto scrollbar-wn min-h-[36px]"
            )}
            rows={1}
          />

        <div className="flex items-center justify-between pb-2">
          <div className="flex items-center gap-2">
            {conversation?.status === "open" && <button
              disabled={disabled}
              className={cn("inline-flex items-center justify-center border border-white",
                "bg-white rounded-lg text-black/90 transition-colors h-[36px] px-2",
                "hover:border-primary/20 hover:bg-primary/10"
                )}
            >
              <Files size={18} className="mr-1"/> Tệp
            </button>}

            {conversation?.status === "open" && <button
              disabled={disabled}
              className={cn("inline-flex items-center justify-center border border-white",
                "bg-white rounded-lg text-black/90 transition-colors h-[36px] px-2",
                "hover:border-primary/20 hover:bg-primary/10"
              )}
            >
              <FileImage size={18} className="mr-1"/> Ảnh
            </button>}

            <button
              disabled={disabled}
              className={cn("inline-flex items-center justify-center border border-white",
                "bg-white rounded-lg text-black/90 transition-colors h-[36px] px-2",
                "hover:border-primary/20 hover:bg-primary/10 "
              )}
            >
              <ShoppingBag size={18} className="mr-1"/> Đơn hàng
            </button>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={onClickSend}
              disabled={disabled || !message.trim()}
              className={cn("inline-flex items-center justify-center",
                "bg-primary hover:bg-primary/80 rounded-full text-white",
                "transition-colors h-[36px] w-[36px] cursor-pointer",)}
            >
              <Send size={18}/>
            </button>
          </div>
        </div>

      </div>
    </div>
  )

}

export default ChatInput;