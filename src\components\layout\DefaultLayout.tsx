import { useState, ReactNode } from "react";
import { Sidebar } from "./Sidebar";
import { AdminFooter } from "./AdminFooter";
import { ThemeSwitcher } from "../theme/ThemeSwitcher";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Menu, X } from "lucide-react";

interface DefaultLayoutProps {
  children: ReactNode;
  showFooter?: boolean;
}

export function DefaultLayout({ children, showFooter = true }: DefaultLayoutProps) {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const handleProfileClick = () => {
    navigate("/profile");
  };

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const handleMobileToggle = (isOpen: boolean) => {
    setMobileMenuOpen(isOpen);
  };

  return (
    <div className="min-h-screen flex flex-col bg-background">
      <div className="flex flex-1">
        {/* Sidebar */}
        <Sidebar
          isCollapsed={sidebarCollapsed}
          onToggle={toggleSidebar}
          onMobileToggle={handleMobileToggle}
        />

        {/* Main content */}
        <div
          className={cn(
            "flex-1 flex flex-col transition-all duration-300",
            sidebarCollapsed ? "md:ml-20" : "md:ml-64"
          )}
        >
          {/* Top navbar */}
          <header className="sticky top-0 z-20 h-16 border-b border-border bg-background/95 backdrop-blur flex items-center px-4 md:px-8">
            {/* Mobile layout: Logo + User menu */}
            <div className="flex items-center justify-between w-full md:hidden">
              {/* Logo placeholder or app name */}
              <div className="flex items-center gap-2 ml-12"> {/* ml-12 để tránh che menu button */}
                <span className="font-semibold text-lg">GHVN</span>
              </div>

              {/* Mobile user menu */}
              <div className="flex items-center gap-2">
                <ThemeSwitcher />
                <DropdownMenu>
                  <DropdownMenuTrigger className="flex items-center">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={`/api/files/content/${user?.avatarId}`} alt={user?.fullName} />
                      <AvatarFallback className="bg-primary text-primary-foreground">
                        {user?.fullName
                          ? user.fullName
                              .split(" ")
                              .map((n) => n[0])
                              .join("")
                          : "U"}
                      </AvatarFallback>
                    </Avatar>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Tài khoản của tôi</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      className="cursor-pointer"
                      onClick={handleProfileClick}
                    >
                      Thông tin cá nhân
                    </DropdownMenuItem>
                    <DropdownMenuItem className="cursor-pointer">
                      Cài đặt
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      className="text-destructive focus:text-destructive cursor-pointer"
                      onClick={logout}
                    >
                      Đăng xuất
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            {/* Desktop layout: Toggle button + User menu */}
            <div className="hidden md:flex items-center justify-between w-full">
              {/* Toggle sidebar button */}
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleSidebar}
              >
                <Menu className="h-5 w-5" />
              </Button>

              <div className="flex items-center gap-4">
                <ThemeSwitcher />

                <DropdownMenu>
                <DropdownMenuTrigger className="flex items-center gap-2">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={`/api/files/content/${user?.avatarId}`} alt={user?.fullName} />
                    <AvatarFallback className="bg-primary text-primary-foreground">
                      {user?.fullName
                        ? user.fullName
                            .split(" ")
                            .map((n) => n[0])
                            .join("")
                        : "U"}
                    </AvatarFallback>
                  </Avatar>
                  <div className="text-left">
                    <p className="text-sm font-medium">{user?.fullName}</p>
                    <p className="text-xs text-muted-foreground">{user?.role}</p>
                  </div>
                </DropdownMenuTrigger>

                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Tài khoản của tôi</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    className="cursor-pointer"
                    onClick={handleProfileClick}
                  >
                    Thông tin cá nhân
                  </DropdownMenuItem>
                  <DropdownMenuItem className="cursor-pointer">
                    Cài đặt
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    className="text-destructive focus:text-destructive cursor-pointer"
                    onClick={logout}
                  >
                    Đăng xuất
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </header>

          {/* Page content */}
          <main className="flex-1 p-4 md:p-8">
            {children}
          </main>

          {/* Footer */}
          {showFooter && <AdminFooter />}
        </div>
      </div>
    </div>
  );
}
