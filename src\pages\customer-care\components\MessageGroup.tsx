import React from "react";
import {Badge} from "@/components/ui/badge.tsx";
import {Message, Conversation} from "@/services/CskhService.ts";
import {cn, formatTime} from '@/lib/utils';
import {<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>} from "lucide-react";
import {Avatar, AvatarFallback, AvatarImage, AvatarBot} from "@/components/ui/avatar.tsx";
import MarkdownRenderer from '@/components/custom/MarkdownRenderer';

interface ChatContentProps {
  groupName: string
  messages: Message[]
  showAvatar: boolean
  user: unknown
  conversation: Conversation
}

const MessageGroup: React.FC<ChatContentProps> = ({
                                                    groupName,
                                                    messages,
                                                    showAvatar,
                                                    user,
                                                    conversation,
                                                  }) => {

  const isUser = user.role === 'shop' || user.role === 'buyer';
  const isStaff = user.role === 'cskh' || user.role === 'admin';


  return (<div>
    <div className="flex items-center justify-center my-4">
      <Badge variant="outline" className="bg-primary/10 border border-primary/20 text-xs">
        {groupName}
      </Badge>
    </div>

    {/* Messages */}
    {messages.map((msg, index) => {

      const toRight = (isUser && msg.senderType === "user") || (isStaff && (msg.senderType === "bot" || msg.senderType === "staff"))
      const sender = msg.senderType === "user" ? conversation?.user : conversation?.staff

      return <div
        key={msg._id}
        className={`flex gap-3 my-2 ${toRight ? 'justify-end' : 'justify-start'}`}
      >
        {(showAvatar && !toRight) && (
          <Avatar className="h-8 w-8 mt-1">
            {msg.senderType === 'bot' ? <AvatarBot/>
              : <>
                <AvatarImage src={sender?.avatarId ? `/api/files/content/${sender.avatarId}` : undefined}
                             alt={sender?.fullName}/>
                <AvatarFallback>{sender?.fullName?.charAt(0)}</AvatarFallback>
              </>}
          </Avatar>
        )}
        <div
          className={cn("max-w-[70%] px-3 py-2 border text-slate-600 rounded-xl",
            toRight && "border-primary/20 bg-primary/10",
            !toRight && "border-slate-200 bg-slate-100",
          )}
        >
          <div className="text-sm break-text">
            <MarkdownRenderer>
              {msg.text}
            </MarkdownRenderer>
          </div>

          <div
            className="flex items-center gap-1 mt-1"
          >
            <span className={`text-xs ${
              toRight ? 'text-muted-foreground' : 'text-slate-400'
            }`}>
             {formatTime(msg.createdAt)}
            </span>

            {msg.read && toRight && (
              <span className="text-xs h-4 px-1">
                <CheckCheck size={16}/>
              </span>
            )}
          </div>

        </div>

        {showAvatar && toRight && (
          <Avatar className="h-8 w-8 mt-1">
            {msg.senderType === 'bot' ? <AvatarBot/>
              : <>
                <AvatarImage
                  src={sender?.avatarId ? `/api/files/content/${sender.avatarId}` : undefined}
                  alt={sender?.fullName}
                />
                <AvatarFallback>{sender?.fullName?.charAt(0)}</AvatarFallback>
              </>}
          </Avatar>
        )}

      </div>
    })}
  </div>)

}

export default MessageGroup;