import React, {useEffect, useState} from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import {cskhService, Conversation, Message} from "@/services/CskhService.ts"
import ChatContent from "@/pages/customer-care/components/ChatContent.tsx";

interface RatingDialogProps {
  isOpen: boolean;
  onClose: () => void;
  conversation: Conversation;
}

const DetailDialog = ({isOpen, onClose, conversation}: RatingDialogProps) => {
  const [messages, setMessages] = useState<Message[]>([]);

  useEffect(() => {
    fetchMessage().then()
  }, [conversation])

  const fetchMessage = async () => {
    if (conversation) {
      const api = await cskhService.getMessages(conversation._id, {pagination: false})
      if (api)
        setMessages(api.data.messages)
    }
  }

  const handleClose = () => {
    onClose();
  };


  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="flex flex-col h-[80vh] mx-auto">
        <DialogHeader className="flex">
          <DialogTitle>Nội dung tin nhắn</DialogTitle>
        </DialogHeader>

        <div className="flex-1 h-full overflow-y-auto">
          {conversation && <ChatContent
              messages={messages}
              conversation={conversation}
              showAvatar={true}
              setNotYetRead={() => {
              }}
          />}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default DetailDialog;