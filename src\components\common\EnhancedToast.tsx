import { useTheme } from "next-themes";
import { Toaster as Sonner, toast as sonnerToast } from "sonner";
import { CheckCircle, XCircle, AlertCircle, Info, X } from "lucide-react";
import { cn } from "@/lib/utils";

type ToasterProps = React.ComponentProps<typeof Sonner>;

// Đ<PERSON>nh nghĩa các biến thể của toast
type ToastVariant = "success" | "error" | "warning" | "info" | "default";

// Định nghĩa các icon cho từng biến thể
const toastIcons: Record<ToastVariant, React.ReactNode> = {
  success: <CheckCircle className="h-5 w-5" />,
  error: <XCircle className="h-5 w-5" />,
  warning: <AlertCircle className="h-5 w-5" />,
  info: <Info className="h-5 w-5" />,
  default: <Info className="h-5 w-5" />,
};

// <PERSON><PERSON><PERSON> ngh<PERSON>a cá<PERSON> mà<PERSON> cho từng biến thể
const toastColors: Record<ToastVariant, string> = {
  success: "bg-green-50 border-green-200 text-green-800 dark:bg-green-950 dark:border-green-900 dark:text-green-300",
  error: "bg-red-50 border-red-200 text-red-800 dark:bg-red-950 dark:border-red-900 dark:text-red-300",
  warning: "bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-950 dark:border-yellow-900 dark:text-yellow-300",
  info: "bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-950 dark:border-blue-900 dark:text-blue-300",
  default: "bg-gray-50 border-gray-200 text-gray-800 dark:bg-gray-900 dark:border-gray-800 dark:text-gray-300",
};

// Định nghĩa các màu cho icon của từng biến thể
const iconColors: Record<ToastVariant, string> = {
  success: "text-green-500 dark:text-green-400",
  error: "text-red-500 dark:text-red-400",
  warning: "text-yellow-500 dark:text-yellow-400",
  info: "text-blue-500 dark:text-blue-400",
  default: "text-gray-500 dark:text-gray-400",
};

// Component EnhancedToaster
export function EnhancedToaster({ ...props }: ToasterProps) {
  const { theme = "system" } = useTheme();

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      className="toaster group"
      toastOptions={{
        classNames: {
          toast: cn(
            "group toast border shadow-lg rounded-lg py-3 px-4",
            "data-[variant=success]:bg-green-50 data-[variant=success]:border-green-200 data-[variant=success]:text-green-800",
            "data-[variant=error]:bg-red-50 data-[variant=error]:border-red-200 data-[variant=error]:text-red-800",
            "data-[variant=warning]:bg-yellow-50 data-[variant=warning]:border-yellow-200 data-[variant=warning]:text-yellow-800",
            "data-[variant=info]:bg-blue-50 data-[variant=info]:border-blue-200 data-[variant=info]:text-blue-800",
            "dark:data-[variant=success]:bg-green-950 dark:data-[variant=success]:border-green-900 dark:data-[variant=success]:text-green-300",
            "dark:data-[variant=error]:bg-red-950 dark:data-[variant=error]:border-red-900 dark:data-[variant=error]:text-red-300",
            "dark:data-[variant=warning]:bg-yellow-950 dark:data-[variant=warning]:border-yellow-900 dark:data-[variant=warning]:text-yellow-300",
            "dark:data-[variant=info]:bg-blue-950 dark:data-[variant=info]:border-blue-900 dark:data-[variant=info]:text-blue-300",
            "dark:bg-gray-900 dark:border-gray-800 dark:text-gray-300"
          ),
          title: "font-medium text-base",
          description: "text-sm opacity-90",
          actionButton: "bg-primary text-primary-foreground",
          cancelButton: "bg-muted text-muted-foreground",
          closeButton: "opacity-70 hover:opacity-100 transition-opacity",
        },
        duration: 4000,
      }}
      {...props}
    />
  );
}

// Hàm toast nâng cấp
interface ToastOptions {
  id?: string;
  title?: string;
  description?: string;
  variant?: ToastVariant;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
  cancel?: {
    label: string;
    onClick: () => void;
  };
  position?: "top-left" | "top-right" | "bottom-left" | "bottom-right" | "top-center" | "bottom-center";
  icon?: React.ReactNode;
  closeButton?: boolean;
}

// Hàm toast chung
function toast(message: string, options?: ToastOptions) {
  const variant = options?.variant || "default";
  const icon = options?.icon || toastIcons[variant];
  
  return sonnerToast(message, {
    id: options?.id,
    duration: options?.duration,
    position: options?.position,
    important: variant === "error",
    icon: (
      <span className={iconColors[variant]}>
        {icon}
      </span>
    ),
    className: toastColors[variant],
    closeButton: options?.closeButton,
    action: options?.action && {
      label: options.action.label,
      onClick: options.action.onClick,
    },
    cancel: options?.cancel && {
      label: options.cancel.label,
      onClick: options.cancel.onClick,
    },
  });
}

// Các hàm toast cho từng biến thể
function success(message: string, options?: Omit<ToastOptions, "variant">) {
  return toast(message, { ...options, variant: "success" });
}

function error(message: string, options?: Omit<ToastOptions, "variant">) {
  return toast(message, { ...options, variant: "error" });
}

function warning(message: string, options?: Omit<ToastOptions, "variant">) {
  return toast(message, { ...options, variant: "warning" });
}

function info(message: string, options?: Omit<ToastOptions, "variant">) {
  return toast(message, { ...options, variant: "info" });
}

// Xuất các hàm và component
export const enhancedToast = {
  success,
  error,
  warning,
  info,
  default: toast,
  promise: sonnerToast.promise,
  dismiss: sonnerToast.dismiss,
  custom: sonnerToast.custom,
};
