import { BaseService, PaginatedResult, PaginationParams } from "./BaseService";

/**
 * Interface cho dữ liệu khách hàng
 */
export interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  address?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho tham số tìm kiếm khách hàng
 */
export interface CustomerSearchParams extends PaginationParams {
  // Có thể thêm các filter đặc biệt cho customer
}

/**
 * CustomerService - Quản lý các API liên quan đến khách hàng
 * Kế thừa từ BaseService để sử dụng các method CRUD cơ bản
 */
class CustomerService extends BaseService<Customer> {
  constructor() {
    super('/customers');
  }
  /**
   * L<PERSON>y danh sách khách hàng có phân trang
   * @param params Tham số tìm kiếm và phân trang
   * @returns Kết quả phân trang với danh sách khách hàng
   */
  async getCustomers(params: CustomerSearchParams = {}): Promise<PaginatedResult<Customer>> {
    return this.getAllPaginateBase(params);
  }

  /**
   * Lấy thông tin chi tiết của một khách hàng
   * @param id ID của khách hàng
   * @returns Thông tin chi tiết khách hàng
   */
  async getCustomerById(id: string): Promise<Customer> {
    return this.getBase(id);
  }

  /**
   * Tạo khách hàng mới
   * @param customerData Dữ liệu khách hàng mới
   * @returns Thông tin khách hàng đã tạo
   */
  async createCustomer(customerData: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>): Promise<Customer> {
    return this.createBase(customerData);
  }

  /**
   * Cập nhật thông tin khách hàng
   * @param id ID của khách hàng
   * @param customerData Dữ liệu cập nhật
   * @returns Thông tin khách hàng sau khi cập nhật
   */
  async updateCustomer(id: string, customerData: Partial<Customer>): Promise<Customer> {
    return this.updateBase(id, customerData);
  }

  /**
   * Xóa khách hàng
   * @param id ID của khách hàng
   * @returns Kết quả xóa
   */
  async deleteCustomer(id: string): Promise<{ success: boolean; message?: string }> {
    return this.deleteBase(id);
  }

  /**
   * Tìm kiếm khách hàng
   * @param query Từ khóa tìm kiếm
   * @param params Tham số phân trang
   * @returns Kết quả tìm kiếm
   */
  async searchCustomers(query: string, params: CustomerSearchParams = {}): Promise<PaginatedResult<Customer>> {
    return this.searchBase(query, params);
  }

  /**
   * Lấy thống kê khách hàng
   * @returns Thống kê khách hàng
   */
  async getCustomerStats(): Promise<Record<string, any>> {
    return this.getStatsBase();
  }

  /**
   * Tìm khách hàng theo email
   * @param email Email khách hàng
   * @returns Thông tin khách hàng hoặc null
   */
  async findByEmail(email: string): Promise<Customer | null> {
    return this.findOneBase({ email });
  }

  /**
   * Tìm khách hàng theo số điện thoại
   * @param phone Số điện thoại khách hàng
   * @returns Thông tin khách hàng hoặc null
   */
  async findByPhone(phone: string): Promise<Customer | null> {
    return this.findOneBase({ phone });
  }

  /**
   * Export danh sách khách hàng
   * @param params Tham số filter
   * @param format Định dạng export
   * @returns File export
   */
  async exportCustomers(params: CustomerSearchParams = {}, format: 'csv' | 'excel' | 'json' = 'csv'): Promise<Blob> {
    return this.exportBase(params, format);
  }

  /**
   * Import danh sách khách hàng
   * @param file File import
   * @param options Tùy chọn import
   * @returns Kết quả import
   */
  async importCustomers(file: File, options?: Record<string, any>): Promise<{ success: boolean; imported: number; errors?: any[]; message?: string }> {
    return this.importBase(file, options);
  }

}

// Export instance của service
export const customerService = new CustomerService();
