import {api} from "@/lib/api";
import {BaseService, PaginatedResponse, BackendPaginatedResponse, QueryParams} from "./BaseService";

/**
 * Interface cho file đính kèm
 */
export interface MessageAttachment {
  fileId: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  fileUrl: string;
}

/**
 * Interface cho tin nhắn chat
 */
export interface ChatMessage {
  _id: string;
  conversationId: string;
  senderId: string | {
    _id: string;
    fullName: string;
    email: string;
    phone?: string;
    avatarId?: string;
  };
  senderRole?: 'buyer' | 'shop' | 'support';
  text?: string; // Backend field
  content?: string; // Frontend field (alias for text)
  attachments?: MessageAttachment[];
  sentAt: string;
  read: boolean;
  readAt?: string;
  createdAt?: string;
  updatedAt?: string;
  isTemporary?: boolean; // Flag for temporary messages
  // Populated fields (legacy)
  sender?: {
    _id: string;
    fullName: string;
    email: string;
    phone?: string;
    avatarId?: string;
  };
}

/**
 * Interface cho cuộc hội thoại
 */
export interface Conversation {
  _id: string;
  // customerId: string;
  customerType: 'buyer' | 'shop';
  // supportId?: string;
  status: 'open' | 'closed';
  closedAt?: string;
  tags?: string[];
  createdBy: string;
  updatedBy?: string;
  createdAt: string;
  updatedAt: string;
  // Populated fields
  customerId?: {
    _id: string;
    fullName: string;
    email: string;
    phone?: string;
    avatarId?: string;
  };
  supportId?: {
    _id: string;
    fullName: string;
    email: string;
    phone?: string;
    avatarId?: string;
  };
  // Additional computed fields
  lastMessage?: ChatMessage;
  unreadCount?: number;
}

/**
 * Interface cho tham số tìm kiếm cuộc hội thoại
 */
export interface ConversationSearchParams {
  page?: number;
  pageSize?: number;
  populate?: string[];
  query?: object;
  sort?: string; // Sort parameter like "-updatedAt" or "createdAt"
  searchFields?: string[]; // Fields to search in
  searchValue?: string; // Search value
}

/**
 * Interface cho tham số tìm kiếm tin nhắn
 */
export interface MessageSearchParams {
  query?: object; // Additional query conditions to merge with conversationId
  page?: number;
  pageSize?: number;
  populate?: string[];
  sort?: string; // Sort parameter like "createdAt" or "-createdAt"
  searchFields?: string[]; // Fields to search in
  searchValue?: string; // Search value
}

/**
 * Interface cho kết quả phân trang (khớp với response từ backend)
 */
export interface PaginatedResult<T> {
  rows: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

/**
 * Interface cho thống kê cuộc hội thoại
 */
export interface ConversationStats {
  total: number;
  open: number;
  closed: number;
  byCustomerType: {
    buyer: number;
    shop: number;
  };
  bySupport: Array<{
    supportId: string;
    supportName: string;
    count: number;
  }>;
}

/**
 * API endpoints constants
 */
const API = {
  CONVERSATIONS: '/conversations',
  CONVERSATION_ID: '/conversations/:id',
  MESSAGES: '/messages',
  MESSAGE_ID: '/messages/:id',
  MESSAGES_BY_CONVERSATION: '/messages/conversation/:conversationId',
  MESSAGES_BY_CONVERSATION_ID: '/messages/:conversationId',
  FILES_UPLOAD: '/files/upload'
};

/**
 * ChatService - Quản lý các API liên quan đến chat
 * Kế thừa từ BaseService để sử dụng các method CRUD cơ bản
 */
class ChatService extends BaseService<ChatMessage> {
  constructor() {
    super('/messages');
  }

  /**
   * Lấy danh sách cuộc hội thoại có phân trang sử dụng BaseService getAllPaginate
   * @param params Tham số tìm kiếm và phân trang
   * @returns Kết quả phân trang với danh sách cuộc hội thoại
   */
  async getConversations(params: ConversationSearchParams = {}): Promise<PaginatedResult<Conversation>> {
    // Build complex query object
    const queryObject: object = {
      ...params.query // Additional query conditions
    };

    const query: QueryParams = {
      page: params.page || 1,
      pageSize: params.pageSize || 20,
      query: Object.keys(queryObject).length > 0 ? queryObject : undefined,
      sort: params.sort || '-updatedAt', // Sort by most recent conversations first
      searchFields: params.searchFields,
      searchValue: params.searchValue
    };

    const result = await this.getAllPaginate<Conversation>(
      API.CONVERSATIONS,
      query,
      params.populate || ['customerId', 'supportId'],
      true,
      false
    );

    if (!result) {
      return {
        rows: [],
        total: 0,
        page: params.page || 1,
        pageSize: params.pageSize || 20,
        totalPages: 0
      };
    }

    // Convert PaginatedResponse to PaginatedResult (backend format)
    return {
      rows: result.data,
      total: result.pagination.total,
      page: result.pagination.page,
      pageSize: result.pagination.pageSize,
      totalPages: result.pagination.totalPages
    };
  }

  /**
   * Lấy thông tin chi tiết của một cuộc hội thoại
   * @param id ID của cuộc hội thoại
   * @param populate Các trường cần populate
   * @returns Thông tin chi tiết cuộc hội thoại
   */
  async getConversationById(id: string, populate?: string[]): Promise<Conversation> {
    const queryParams = new URLSearchParams();
    if (populate) queryParams.append('populate', populate.join(','));

    const url = `/conversations/${id}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return api.get<Conversation>(url);
  }

  /**
   * Lấy tin nhắn theo cuộc hội thoại sử dụng BaseService getAllPaginate
   * @param params Tham số phân trang
   * @returns Danh sách tin nhắn với format backend
   */
  async getMessages(params: MessageSearchParams = {}
  ): Promise<PaginatedResult<ChatMessage>> {
    // Build complex query object
    const queryObject: object = {
      ...params.query // Additional query conditions
    };

    const query: QueryParams = {
      page: params.page || 1,
      pageSize: params.pageSize || 20,
      query: queryObject,
      sort: params.sort || 'createdAt', // Sort messages by creation time (oldest first)
      searchFields: params.searchFields,
      searchValue: params.searchValue
    };
    const result = await this.getAllPaginate<ChatMessage>(
      API.MESSAGES,
      query,
      params.populate || ['senderId'],
      true,
      false
    );

    if (!result) {
      return {
        rows: [],
        total: 0,
        page: params.page || 1,
        pageSize: params.pageSize || 20,
        totalPages: 0
      };
    }

    // Convert PaginatedResponse to PaginatedResult (backend format)
    return {
      rows: result.data,
      total: result.pagination.total,
      page: result.pagination.page,
      pageSize: result.pagination.pageSize,
      totalPages: result.pagination.totalPages
    };
  }

  /**
   * Lấy tin nhắn theo cuộc hội thoại với format BaseService (PaginatedResponse)
   * @param conversationId ID của cuộc hội thoại
   * @param params Tham số phân trang
   * @returns Danh sách tin nhắn với format BaseService
   */
  async getMessagesWithBaseFormat(
    conversationId: string,
    params: MessageSearchParams = {}
  ): Promise<PaginatedResponse<ChatMessage> | null> {
    const query: QueryParams = {
      conversationId,
      page: params.page || 1,
      pageSize: params.pageSize || 20,
      ...params.query
    };

    return this.getAllPaginate<ChatMessage>(
      API.MESSAGES,
      query,
      params.populate || ['senderId'],
      true,
      false
    );
  }

  /**
   * Lấy tin nhắn theo cuộc hội thoại (legacy method - tương thích với code cũ)
   * @param params Tham số phân trang
   * @returns Danh sách tin nhắn với format PaginatedResult
   */
  async getMessagesByParams(params: MessageSearchParams = {}): Promise<PaginatedResult<ChatMessage>> {
    const result = await this.getAllPaginate<ChatMessage>(
      API.MESSAGES,
      {
        page: params.page || 1,
        pageSize: params.pageSize || 20,
        ...params.query
      },
      params.populate || ['senderId'],
      true,
      false
    );

    if (!result) {
      return {
        rows: [],
        total: 0,
        page: 1,
        pageSize: 20,
        totalPages: 0
      };
    }

    // Convert PaginatedResponse to PaginatedResult (backend format)
    return {
      rows: result.data,
      total: result.pagination.total,
      page: result.pagination.page,
      pageSize: result.pagination.pageSize,
      totalPages: result.pagination.totalPages
    };
  }

  /**
   * Lấy tất cả tin nhắn theo cuộc hội thoại (không phân trang)
   * @param conversationId ID của cuộc hội thoại
   * @param options Tùy chọn query và populate
   * @returns Tất cả tin nhắn của cuộc hội thoại
   */
  async getMessagesByConversationId(
    conversationId: string,
    options: {
      query?: object;
      sort?: string;
      populate?: string[];
    } = {}
  ): Promise<ChatMessage[]> {
    const query: QueryParams = {
      ...options.query,
      sort: options.sort || 'createdAt' // Sort messages by creation time (oldest first)
    };

    const result = await this.getAll<ChatMessage>(
      `/messages/${conversationId}`,
      query,
      options.populate || ['senderId'],
      true,
      false
    );

    return result || [];
  }

  /**
   * Lấy tất cả tin nhắn theo cuộc hội thoại - gọi trực tiếp endpoint (không thông qua BaseService)
   * @param conversationId ID của cuộc hội thoại
   * @returns Tất cả tin nhắn của cuộc hội thoại
   */
  async getMessagesByConversationIdDirect(conversationId: string): Promise<ChatMessage[]> {
    const url = `/api/messages/${conversationId}`;

    try {
      // Gọi trực tiếp API endpoint
      const result = await api.get<ChatMessage[]>(url);
      return result || [];
    } catch (error) {
      console.error('Error fetching messages by conversation ID:', error);
      return [];
    }
  }

  /**
   * Gửi tin nhắn mới sử dụng BaseService create
   * @param conversationId ID của cuộc hội thoại
   * @param content Nội dung tin nhắn
   * @param attachments Danh sách file đính kèm
   * @returns Tin nhắn đã gửi
   */
  async sendMessage(
    conversationId: string,
    content: string,
    attachments: MessageAttachment[] = []
  ): Promise<ChatMessage | null> {
    const messageData = {
      conversationId,
      text: content, // Backend expects 'text' field
      attachments
    };

    return this.create<ChatMessage>(
      API.MESSAGES,
      messageData,
      ['senderId'],
      true,
      true
    );
  }

  /**
   * Đánh dấu tin nhắn đã đọc
   * @param messageId ID của tin nhắn
   * @returns Kết quả cập nhật
   */
  async markMessageAsRead(messageId: string): Promise<{ success: boolean }> {
    return api.put<{ success: boolean }>(`/messages/${messageId}/read`);
  }

  /**
   * Đánh dấu tất cả tin nhắn trong cuộc hội thoại đã đọc
   * @param conversationId ID của cuộc hội thoại
   * @returns Kết quả cập nhật
   */
  async markConversationAsRead(conversationId: string): Promise<{ success: boolean }> {
    return api.put<{ success: boolean }>(`/messages/conversation/${conversationId}/read`);
  }

  /**
   * Đếm tin nhắn chưa đọc
   * @returns Số lượng tin nhắn chưa đọc
   */
  async getUnreadCount(): Promise<{ unreadCount: number }> {
    return api.get<{ unreadCount: number }>('/messages/unread/count');
  }

  /**
   * Tạo cuộc hội thoại mới
   * @param customerId ID khách hàng
   * @param customerType Loại khách hàng
   * @param supportId ID nhân viên hỗ trợ (optional)
   * @param tags Tags phân loại (optional)
   * @returns Cuộc hội thoại đã tạo
   */
  async createConversation(
    customerId: string,
    customerType: 'buyer' | 'shop',
    supportId?: string,
    tags?: string[]
  ): Promise<Conversation> {
    return api.post<Conversation>('/conversations', {
      customerId,
      customerType,
      supportId,
      tags
    });
  }

  /**
   * Cập nhật cuộc hội thoại
   * @param id ID cuộc hội thoại
   * @param data Dữ liệu cập nhật
   * @returns Cuộc hội thoại đã cập nhật
   */
  async updateConversation(id: string, data: Partial<Conversation>): Promise<Conversation> {
    return api.put<Conversation>(`/conversations/${id}`, data);
  }

  /**
   * Đóng cuộc hội thoại
   * @param id ID cuộc hội thoại
   * @returns Cuộc hội thoại đã đóng
   */
  async closeConversation(id: string): Promise<Conversation> {
    return api.put<Conversation>(`/conversations/${id}/close`);
  }

  /**
   * Mở lại cuộc hội thoại
   * @param id ID cuộc hội thoại
   * @returns Cuộc hội thoại đã mở lại
   */
  async reopenConversation(id: string): Promise<Conversation> {
    return api.put<Conversation>(`/conversations/${id}/reopen`);
  }

  /**
   * Gán nhân viên hỗ trợ
   * @param id ID cuộc hội thoại
   * @param supportId ID nhân viên hỗ trợ
   * @returns Cuộc hội thoại đã cập nhật
   */
  async assignSupport(id: string, supportId: string): Promise<Conversation> {
    return api.put<Conversation>(`/conversations/${id}/assign`, {supportId});
  }

  /**
   * Thêm tags
   * @param id ID cuộc hội thoại
   * @param tags Danh sách tags
   * @returns Cuộc hội thoại đã cập nhật
   */
  async addTags(id: string, tags: string[]): Promise<Conversation> {
    return api.put<Conversation>(`/conversations/${id}/tags/add`, {tags});
  }

  /**
   * Lấy thống kê cuộc hội thoại
   * @returns Thống kê cuộc hội thoại
   */
  async getStats(): Promise<ConversationStats> {
    return api.get<ConversationStats>('/conversations/stats');
  }
  /**
   * Tìm kiếm tin nhắn
   * @param searchValue Từ khóa tìm kiếm
   * @param conversationId ID cuộc hội thoại (optional)
   * @param params Tham số phân trang
   * @returns Kết quả tìm kiếm tin nhắn
   */
  async searchMessages(
    searchValue: string,
    conversationId?: string,
    params: MessageSearchParams = {}
  ): Promise<PaginatedResponse<ChatMessage> | null> {
    const query: QueryParams = {
      page: params.page || 1,
      pageSize: params.pageSize || 20,
      ...params.query
    };

    if (conversationId) {
      query.conversationId = conversationId;
    }

    return this.searchPaginate<ChatMessage>(
      API.MESSAGES,
      ['text'], // Search in text field
      searchValue,
      query,
      params.populate || ['senderId'],
      true,
      false
    );
  }

  /**
   * Đếm tin nhắn theo điều kiện
   * @param query Điều kiện đếm
   * @returns Số lượng tin nhắn
   */
  async countMessages(query: QueryParams = {}): Promise<number | null> {
    return this.count<number>(`${API.MESSAGES}/count`, query);
  }
}

// Export instance của service
export const chatService = new ChatService();
