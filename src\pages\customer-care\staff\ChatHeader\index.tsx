import React from 'react';
import {Avatar, AvatarFallback, AvatarImage} from '@/components/ui/avatar.tsx';
import {Button} from '@/components/ui/button.tsx';
import {ArrowLeft, MoreVertical} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu.tsx';
import {Conversation, cskhService} from '@/services/CskhService.ts';
import {ChatStatusButton} from '../StatusUpdata';
import {toast} from "@/hooks/use-toast.ts";

interface ChatHeaderProps {
  disabled?: boolean;
  isSmallScreen?: boolean;
  conversation: Conversation | null;
  backToSidebar: (room_id?: string) => void;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({
                                                 disabled,
                                                 isSmallScreen = false,
                                                 conversation,
                                                 backToSidebar,
                                               }) => {
  if (!conversation) {
    return (
      <div className="h-16 border-b border-border bg-card flex items-center justify-center">
        <p className="text-muted-foreground"><PERSON><PERSON>n một cuộc trò chuyện để bắt đầu</p>
      </div>
    );
  }

  const handleStatusChange = async (newStatus) => {
    const room_id = conversation._id
    const body_update = {status: newStatus}
    const api = await cskhService.updateStatus(room_id, body_update)
    if (api)
      toast({
        title: "Thành công",
        description: "Cập nhật trạng thái cuộc trò truyện thành công",
      });
  };

  return (
    <div className="h-16 border-b border-border bg-card flex items-center justify-between px-4">
      {/* User Info */}
      <div className="flex items-center gap-3">
        {isSmallScreen && <Button
            className="flex"
            variant="ghost"
            onClick={() => backToSidebar(conversation._id)}
            size="sm"
        >
            <ArrowLeft size={16}/>
        </Button>}

        <Avatar className="h-10 w-10">
          <AvatarImage
            src={conversation.ownerUserId ? `/api/files/content/${conversation.ownerUserId}` : undefined}
            alt={conversation.title}
          />
          <AvatarFallback>{conversation.title?.charAt(0)}</AvatarFallback>
        </Avatar>

        <div>
          <h3 className="font-semibold text-md">{conversation.title}</h3>
          <div className="flex items-center gap-2 text-sm">
            {conversation.topic.title}
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center gap-2">
        <ChatStatusButton
          conversation={conversation}
          status={conversation.status}
          disabled={disabled}
          onStatusChange={handleStatusChange}
        />


        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <MoreVertical className="h-4 w-4"/>
            </Button>
          </DropdownMenuTrigger>
          {/*<DropdownMenuContent align="end">*/}
          {/*  {onViewProfile && (*/}
          {/*    <DropdownMenuItem onClick={onViewProfile}>*/}
          {/*      <User className="h-4 w-4 mr-2"/>*/}
          {/*      Xem thông tin khách hàng*/}
          {/*    </DropdownMenuItem>*/}
          {/*  )}*/}
          {/*</DropdownMenuContent>*/}
        </DropdownMenu>
      </div>
    </div>
  );
};

export default ChatHeader;