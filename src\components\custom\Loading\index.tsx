
import React from 'react';
import { cn } from '@/lib/utils';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  text?: string;
}

const Loading = ({ size = 'md', className, text = '<PERSON>ang tải...' }: LoadingProps) => {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };

  return (
    <div className={cn(
      "flex flex-col items-center justify-center min-h-full w-full",
      className
    )}>
      <div className="flex flex-col items-center space-y-4">
        {/* Spinner */}
        <div
          className={cn(
            "animate-spin rounded-full border-4 border-gray-200 border-t-primary",
            sizeClasses[size]
          )}
        />

        {/* Loading text */}
        {text && (
          <p className={cn(
            "text-muted-foreground font-medium animate-pulse",
            textSizeClasses[size]
          )}>
            {text}
          </p>
        )}
      </div>
    </div>
  );
};

export default Loading;
