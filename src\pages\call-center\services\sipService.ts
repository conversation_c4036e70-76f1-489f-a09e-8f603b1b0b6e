import { Web } from 'sip.js';
import { SIP_CONFIG } from '../config/sipConfig';
import { ringToneService } from './ringToneService';

export enum CallStatus {
    IDLE = 'idle',
    CONNECTING = 'connecting',
    CONNECTED = 'connected',
    DISCONNECTED = 'disconnected',
    INCOMING = 'incoming',
    OUTGOING = 'outgoing',
    ERROR = 'error'
}

export interface CallState {
    status: CallStatus;
    remoteIdentity?: string;
    startTime?: Date;
    endTime?: Date;
    duration?: number;
    error?: string;
    recording?: boolean;
}

class SipService {
    private simpleUser: Web.SimpleUser | null = null;
    private remoteAudio: HTMLAudioElement | null = null;
    private localStream: MediaStream | null = null;
    private callState: CallState = { status: CallStatus.IDLE };
    private onCallStateChangeCallbacks: ((state: CallState) => void)[] = [];

    constructor() {
        // Tạo audio element để phát âm thanh từ cuộc gọi
        this.remoteAudio = document.createElement('audio');
        this.remoteAudio.autoplay = true;
    }

    /**
     * Khởi tạo kết nối SIP
     */
    public async initialize(username: string = SIP_CONFIG.DEFAULT_USER, password: string = SIP_CONFIG.DEFAULT_PASSWORD): Promise<void> {
        try {
            // Cấu hình SIP server - Sử dụng WebSocket URL từ cấu hình
            const server = SIP_CONFIG.WEBSOCKET_URL;
            const aor = `sip:${username}@${SIP_CONFIG.SERVER_URL.split(':')[0]}`;

            console.log('Connecting to SIP server with:', {
                server,
                aor,
                username,
                domain: SIP_CONFIG.SERVER_URL.split(':')[0]
            });

            // Tạo SimpleUser từ SIP.js
            const options: Web.SimpleUserOptions = {
                aor,
                userAgentOptions: {
                    authorizationUsername: username,
                    authorizationPassword: password
                },
                media: {
                    remote: {
                        audio: this.remoteAudio || undefined
                    }
                }
            };

            this.simpleUser = new Web.SimpleUser(server, options);

            // Đăng ký các sự kiện
            this.registerEvents();

            // Kết nối đến SIP server
            await this.simpleUser.connect();
            await this.simpleUser.register();

            this.updateCallState({ status: CallStatus.IDLE });

            console.log('SIP service initialized successfully');
        } catch (error) {
            console.error('Failed to initialize SIP service', error);

            // Xử lý thông báo lỗi chi tiết hơn
            let errorMessage = 'Không thể kết nối đến máy chủ SIP';

            if (error instanceof Error) {
                if (error.message.includes('WebSocket')) {
                    errorMessage = `Lỗi kết nối WebSocket: ${error.message}`;
                } else if (error.message.includes('Authorization')) {
                    errorMessage = 'Lỗi xác thực: Tên đăng nhập hoặc mật khẩu không đúng';
                } else {
                    errorMessage = `Lỗi: ${error.message}`;
                }

                console.log('Detailed error:', {
                    name: error.name,
                    message: error.message,
                    stack: error.stack
                });
            }

            this.updateCallState({
                status: CallStatus.ERROR,
                error: errorMessage
            });
        }
    }

    /**
     * Format thông tin hiển thị cho người gọi
     */
    private formatCallerDisplay(callerInfo: string | null): string {
        if (!callerInfo) {
            return 'Cuộc gọi đến từ khách hàng';
        }

        // Kiểm tra xem có phải là số điện thoại không
        if (/^\d+$/.test(callerInfo)) {
            return `Cuộc gọi đến từ khách hàng (${callerInfo})`;
        } else {
            // Nếu là tên hoặc username
            return `Cuộc gọi đến từ ${callerInfo}`;
        }
    }

    /**
     * Trích xuất thông tin người gọi từ session
     */
    private extractCallerInfo(): string | null {
        try {
            const session = this.simpleUser?.session;
            if (!session) return null;

            // Thử lấy display name trước
            const displayName = session.remoteIdentity?.displayName;
            if (displayName && displayName.trim() && displayName !== 'Unknown') {
                return displayName.trim();
            }

            // Lấy thông tin từ remote URI
            const remoteUri = session.remoteIdentity?.uri;
            if (remoteUri) {
                const uriString = remoteUri.toString();

                // Trích xuất số điện thoại hoặc username từ URI (sip:user@domain)
                const match = uriString.match(/sip:([^@]+)/);
                if (match && match[1]) {
                    const callerInfo = match[1];

                    // Kiểm tra xem có phải là số điện thoại không
                    if (/^\d+$/.test(callerInfo)) {
                        return callerInfo; // Trả về số điện thoại
                    } else if (callerInfo !== 'anonymous' && callerInfo !== 'unknown') {
                        return callerInfo; // Trả về username
                    }
                }
            }

            // Thử lấy từ request URI nếu có
            const requestUri = session.request?.uri;
            if (requestUri) {
                const match = requestUri.toString().match(/sip:([^@]+)/);
                if (match && match[1]) {
                    return match[1];
                }
            }

            return null;
        } catch (error) {
            console.warn('Không thể lấy thông tin người gọi:', error);
            return null;
        }
    }

    /**
     * Đăng ký các sự kiện cho SimpleUser
     */
    private registerEvents(): void {
        if (!this.simpleUser) return;

        // Xử lý cuộc gọi đến
        this.simpleUser.delegate = {
            onCallReceived: () => {
                // Cố gắng lấy thông tin người gọi từ session
                const callerInfo = this.extractCallerInfo();
                const remoteIdentity = this.formatCallerDisplay(callerInfo);

                // Bắt đầu phát chuông báo cho cuộc gọi đến (ring-coming.mp3)
                ringToneService.startIncomingRing();

                this.updateCallState({
                    status: CallStatus.INCOMING,
                    remoteIdentity
                });
            },
            onCallHangup: () => {
                const endTime = new Date();
                const duration = this.callState.startTime
                    ? Math.floor((endTime.getTime() - this.callState.startTime.getTime()) / 1000)
                    : 0;

                // Dừng chuông báo nếu đang phát
                ringToneService.stopRinging();

                this.updateCallState({
                    status: CallStatus.DISCONNECTED,
                    endTime,
                    duration
                });

                // Dọn dẹp sau khi kết thúc cuộc gọi
                this.cleanupAfterCall();
            },
            onCallAnswered: () => {
                // Dừng chuông báo khi trả lời cuộc gọi
                ringToneService.stopRinging();

                this.updateCallState({
                    status: CallStatus.CONNECTED,
                    startTime: new Date()
                });
            }
        };
    }

    /**
     * Thực hiện cuộc gọi đi
     * @param destination Số điện thoại hoặc extension cần gọi
     * @param options Tùy chọn cho cuộc gọi
     */
    public async makeCall(destination: string, options: { skipMediaAccess?: boolean } = {}): Promise<void> {
        if (!this.simpleUser) {
            throw new Error('SIP service not initialized');
        }

        try {
            // Cập nhật trạng thái
            this.updateCallState({
                status: CallStatus.CONNECTING,
                remoteIdentity: destination
            });

            // Yêu cầu quyền truy cập microphone nếu không bỏ qua
            if (!options.skipMediaAccess) {
                try {
                    this.localStream = await navigator.mediaDevices.getUserMedia({ audio: true });
                } catch (mediaError) {
                    console.warn('Không thể truy cập microphone:', mediaError);

                    // Hiển thị thông báo chi tiết về lỗi microphone
                    if (mediaError instanceof Error) {
                        if (mediaError.name === 'NotFoundError') {
                            console.error('Không tìm thấy thiết bị âm thanh (microphone)');
                            throw new Error('Không tìm thấy thiết bị âm thanh (microphone). Vui lòng kết nối microphone và thử lại.');
                        } else if (mediaError.name === 'NotAllowedError') {
                            console.error('Quyền truy cập microphone bị từ chối');
                            throw new Error('Quyền truy cập microphone bị từ chối. Vui lòng cấp quyền truy cập microphone cho trang web này.');
                        } else if (mediaError.name === 'AbortError') {
                            console.error('Thiết bị âm thanh đang được sử dụng bởi ứng dụng khác');
                            throw new Error('Thiết bị âm thanh đang được sử dụng bởi ứng dụng khác. Vui lòng đóng các ứng dụng khác đang sử dụng microphone.');
                        }
                    }

                    // Nếu không phải lỗi cụ thể, ném lại lỗi
                    throw mediaError;
                }
            }

            // Thực hiện cuộc gọi
            const sipUri = `sip:${destination}@${SIP_CONFIG.SERVER_URL.split(':')[0]}`;
            console.log(`Đang gọi đến: ${sipUri}`);

            // Truyền tùy chọn phù hợp vào phương thức call
            const callOptions = options.skipMediaAccess ?
                { inviteWithoutSdp: true } : // Không yêu cầu SDP offer khi không cần microphone
                undefined;

            console.log(`Gọi với tùy chọn:`, callOptions);
            await this.simpleUser.call(sipUri, callOptions);

            // Bắt đầu phát chuông chờ cho cuộc gọi đi (ring-wait.mp3)
            ringToneService.startOutgoingRing();

            // Cập nhật trạng thái
            this.updateCallState({
                status: CallStatus.OUTGOING
            });
        } catch (error) {
            console.error('Failed to make call', error);

            // Xử lý thông báo lỗi chi tiết
            let errorMessage = 'Không thể thực hiện cuộc gọi';

            if (error instanceof Error) {
                errorMessage = error.message;
            }

            this.updateCallState({
                status: CallStatus.ERROR,
                error: errorMessage
            });

            this.cleanupAfterCall();
        }
    }

    /**
     * Trả lời cuộc gọi đến
     * @param options Tùy chọn cho việc trả lời cuộc gọi
     */
    public async answerCall(options: { skipMediaAccess?: boolean } = {}): Promise<void> {
        if (!this.simpleUser || this.callState.status !== CallStatus.INCOMING) {
            throw new Error('Không có cuộc gọi đến để trả lời');
        }

        try {
            // Dừng chuông báo khi bắt đầu trả lời
            ringToneService.stopRinging();

            // Yêu cầu quyền truy cập microphone nếu không bỏ qua
            if (!options.skipMediaAccess) {
                try {
                    this.localStream = await navigator.mediaDevices.getUserMedia({ audio: true });
                } catch (mediaError) {
                    console.warn('Không thể truy cập microphone:', mediaError);

                    // Hiển thị thông báo chi tiết về lỗi microphone
                    if (mediaError instanceof Error) {
                        if (mediaError.name === 'NotFoundError') {
                            console.error('Không tìm thấy thiết bị âm thanh (microphone)');
                            throw new Error('Không tìm thấy thiết bị âm thanh (microphone). Vui lòng kết nối microphone và thử lại.');
                        } else if (mediaError.name === 'NotAllowedError') {
                            console.error('Quyền truy cập microphone bị từ chối');
                            throw new Error('Quyền truy cập microphone bị từ chối. Vui lòng cấp quyền truy cập microphone cho trang web này.');
                        } else if (mediaError.name === 'AbortError') {
                            console.error('Thiết bị âm thanh đang được sử dụng bởi ứng dụng khác');
                            throw new Error('Thiết bị âm thanh đang được sử dụng bởi ứng dụng khác. Vui lòng đóng các ứng dụng khác đang sử dụng microphone.');
                        }
                    }

                    // Nếu không phải lỗi cụ thể, ném lại lỗi
                    throw mediaError;
                }
            }

            // Trả lời cuộc gọi
            // SimpleUser.answer không chấp nhận tùy chọn inviteWithoutSdp
            // Nhưng vì chúng ta đã bỏ qua việc truy cập microphone ở trên,
            // nên chỉ cần gọi answer() mà không cần tùy chọn đặc biệt
            console.log(`Trả lời cuộc gọi với skipMediaAccess=${options.skipMediaAccess}`);
            await this.simpleUser.answer();

            // Cập nhật trạng thái
            this.updateCallState({
                status: CallStatus.CONNECTED,
                startTime: new Date()
            });
        } catch (error) {
            console.error('Failed to answer call', error);

            // Xử lý thông báo lỗi chi tiết
            let errorMessage = 'Không thể trả lời cuộc gọi';

            if (error instanceof Error) {
                errorMessage = error.message;
            }

            this.updateCallState({
                status: CallStatus.ERROR,
                error: errorMessage
            });

            this.cleanupAfterCall();
        }
    }

    /**
     * Từ chối cuộc gọi đến
     */
    public async rejectCall(): Promise<void> {
        if (!this.simpleUser || this.callState.status !== CallStatus.INCOMING) {
            throw new Error('No incoming call to reject');
        }

        try {
            // Dừng chuông báo khi từ chối cuộc gọi
            ringToneService.stopRinging();

            // Từ chối cuộc gọi
            await this.simpleUser.hangup();

            // Cập nhật trạng thái
            this.updateCallState({
                status: CallStatus.IDLE
            });
        } catch (error) {
            console.error('Failed to reject call', error);
            this.updateCallState({
                status: CallStatus.ERROR,
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }

    /**
     * Kết thúc cuộc gọi hiện tại
     */
    public async hangupCall(): Promise<void> {
        if (!this.simpleUser) {
            throw new Error('SIP service not initialized');
        }

        try {
            // Kết thúc cuộc gọi
            await this.simpleUser.hangup();

            // Trạng thái sẽ được cập nhật thông qua sự kiện onCallHangup
        } catch (error) {
            console.error('Failed to hangup call', error);
            this.updateCallState({
                status: CallStatus.ERROR,
                error: error instanceof Error ? error.message : 'Unknown error'
            });
            this.cleanupAfterCall();
        }
    }

    /**
     * Bắt đầu ghi âm cuộc gọi
     */
    public startRecording(): void {
        if (this.callState.status !== CallStatus.CONNECTED) {
            console.warn('Không thể bắt đầu ghi âm: Không có cuộc gọi đang hoạt động');
            return; // Trả về thay vì ném lỗi
        }

        // Trong thực tế, việc ghi âm thường được xử lý ở phía server
        // Đây chỉ là cập nhật trạng thái để UI hiển thị
        this.updateCallState({
            recording: true
        });

        console.log('Call recording started');
    }

    /**
     * Dừng ghi âm cuộc gọi
     */
    public stopRecording(): void {
        if (!this.callState.recording) {
            console.warn('Không thể dừng ghi âm: Không có ghi âm đang hoạt động');
            return; // Trả về thay vì ném lỗi
        }

        // Cập nhật trạng thái
        this.updateCallState({
            recording: false
        });

        console.log('Call recording stopped');
    }

    /**
     * Tắt/bật microphone
     */
    public toggleMute(mute: boolean): void {
        // Cho phép tắt/bật microphone trong cả trạng thái CONNECTED và OUTGOING
        if (!this.simpleUser ||
            (this.callState.status !== CallStatus.CONNECTED &&
             this.callState.status !== CallStatus.OUTGOING)) {
            console.warn('Không thể tắt/bật microphone: Không có cuộc gọi đang hoạt động');
            return; // Trả về thay vì ném lỗi
        }

        if (this.localStream) {
            this.localStream.getAudioTracks().forEach(track => {
                track.enabled = !mute;
            });
            console.log(`Microphone ${mute ? 'muted' : 'unmuted'}`);
        } else {
            console.warn('Không thể tắt/bật microphone: Không có stream âm thanh');
        }
    }

    /**
     * Đăng ký callback để nhận thông báo khi trạng thái cuộc gọi thay đổi
     */
    public onCallStateChange(callback: (state: CallState) => void): void {
        this.onCallStateChangeCallbacks.push(callback);
    }

    /**
     * Cập nhật trạng thái cuộc gọi và thông báo cho các callback
     */
    private updateCallState(newState: Partial<CallState>): void {
        this.callState = { ...this.callState, ...newState };

        // Thông báo cho tất cả các callback
        this.onCallStateChangeCallbacks.forEach(callback => {
            callback(this.callState);
        });
    }

    /**
     * Dọn dẹp tài nguyên sau khi kết thúc cuộc gọi
     */
    private cleanupAfterCall(): void {
        // Dừng các track media
        if (this.localStream) {
            this.localStream.getTracks().forEach(track => track.stop());
            this.localStream = null;
        }
    }

    /**
     * Hủy kết nối SIP
     */
    public async disconnect(): Promise<void> {
        if (!this.simpleUser) return;

        try {
            // Dừng chuông báo nếu đang phát
            ringToneService.stopRinging();

            // Kết thúc cuộc gọi nếu đang có
            if (this.callState.status === CallStatus.CONNECTED ||
                this.callState.status === CallStatus.CONNECTING ||
                this.callState.status === CallStatus.INCOMING ||
                this.callState.status === CallStatus.OUTGOING) {
                await this.hangupCall();
            }

            // Hủy đăng ký và ngắt kết nối
            await this.simpleUser.unregister();
            await this.simpleUser.disconnect();

            // Dọn dẹp
            this.cleanupAfterCall();
            this.simpleUser = null;

            // Cập nhật trạng thái
            this.updateCallState({ status: CallStatus.IDLE });

            console.log('SIP service disconnected');
        } catch (error) {
            console.error('Failed to disconnect SIP service', error);
        }
    }
}

// Export singleton instance
export const sipService = new SipService();
export default sipService;
