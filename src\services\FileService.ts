import { api } from '@/lib/api';
import { API_CONFIG } from '@/config/env';

/**
 * <PERSON>ác endpoint cho file service
 * - Upload file: Sử dụng đường dẫn từ cấu hình môi trường (được proxy trự<PERSON> tiếp)
 * - Các API khác: Sử dụng đường dẫn /api/... (qua API proxy)
 */

/**
 * Interface cho kết quả upload file
 */
export interface FileUploadResult {
  _id: string;
  ownerId: string;
  name: string;
  displayName: string;
  fileType: string;
  mimetype: string;
  size: string;
  storageType: string;
  storageLocation: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho callback tiến trình upload
 */
export interface UploadProgressCallback {
  onProgress?: (progress: number) => void;
  onSuccess?: (result: FileUploadResult) => void;
  onError?: (error: Error) => void;
}

/**
 * FileService - Quản lý các API liên quan đến upload file
 */
class FileService {
  /**
   * Upload file lên server
   * @param file File cần upload
   * @param callbacks Các callback để theo dõi tiến trình
   * @returns Thông tin file đã upload
   */
  async uploadFile(file: File, callbacks?: UploadProgressCallback): Promise<FileUploadResult> {
    const { onProgress, onSuccess, onError } = callbacks || {};
    const formData = new FormData();
    formData.append('file', file);

    try {
      // Sử dụng XMLHttpRequest để có thể theo dõi tiến trình
      return await new Promise<FileUploadResult>((resolve, reject) => {
        const xhr = new XMLHttpRequest();

        // Theo dõi tiến trình upload
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100);
            onProgress?.(progress);
          }
        });

        // Xử lý khi hoàn thành
        xhr.addEventListener('load', () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            try {
              const result = JSON.parse(xhr.responseText) as FileUploadResult;
              onSuccess?.(result);
              resolve(result);
            } catch (error) {
              const parseError = new Error('Lỗi khi phân tích kết quả từ server');
              onError?.(parseError);
              reject(parseError);
            }
          } else {
            let errorMessage = `Lỗi upload: ${xhr.status}`;
            try {
              const errorData = JSON.parse(xhr.responseText);
              errorMessage = errorData.message || errorMessage;
            } catch (e) {
              // Ignore JSON parse error
            }
            const uploadError = new Error(errorMessage);
            onError?.(uploadError);
            reject(uploadError);
          }
        });

        // Xử lý lỗi
        xhr.addEventListener('error', () => {
          const networkError = new Error('Lỗi kết nối khi upload file');
          onError?.(networkError);
          reject(networkError);
        });

        // Xử lý hủy
        xhr.addEventListener('abort', () => {
          const abortError = new Error('Upload đã bị hủy');
          onError?.(abortError);
          reject(abortError);
        });

        // Mở kết nối và gửi request
        xhr.open('POST', API_CONFIG.UPLOAD_URL, true);
        xhr.withCredentials = true; // Đảm bảo cookies được gửi kèm
        xhr.send(formData);
      });
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Lỗi kết nối khi upload file');
    }
  }

  /**
   * Lấy thông tin file theo ID
   * @param fileId ID của file
   * @returns Thông tin file
   */
  async getFileById(fileId: string): Promise<FileUploadResult> {
    // Sử dụng API proxy để gọi đến backend server
    return api.get<FileUploadResult>(`/files/${fileId}`);
  }

  /**
   * Lấy URL hiển thị nội dung file
   * @param fileId ID của file
   * @returns URL hiển thị nội dung file
   */
  getFileContentUrl(fileId: string): string {
    if (!fileId) return '';
    return `${API_CONFIG.FILE_CONTENT_URL}/${fileId}`;
  }
}

// Export instance của service
export const fileUploadService = new FileService();
