/**
 * API service for making HTTP requests
 * Configured to work with httpOnly cookies for authentication
 */

import { API_CONFIG } from '@/config/env';

// Sử dụng URL từ cấu hình môi trường
const API_BASE_URL = API_CONFIG.BASE_URL;

interface ApiOptions extends RequestInit {
  params?: Record<string, string>;
}

/**
 * Makes an API request with proper cookie handling
 * @param endpoint - API endpoint path
 * @param options - Request options
 * @returns Promise with response data
 */
export async function apiRequest<T = any>(
  endpoint: string,
  options: ApiOptions = {}
): Promise<T> {
  const { params, ...requestOptions } = options;

  // Build URL with query parameters if provided
  let url = `${API_BASE_URL}${endpoint}`;
  if (params) {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      queryParams.append(key, value);
    });
    url += `?${queryParams.toString()}`;
  }

  // Set default headers
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  // Make the request with credentials to ensure cookies are sent
  const response = await fetch(url, {
    ...requestOptions,
    headers,
    credentials: 'include', // Important: This ensures cookies are sent with the request
  });

  // Handle non-2xx responses
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || 'Đã xảy ra lỗi khi gọi API');
  }

  // Parse and return the response data
  return response.json();
}

/**
 * API methods for common HTTP verbs
 */
export const api = {
  get: <T = any>(endpoint: string, options?: ApiOptions) =>
    apiRequest<T>(endpoint, { method: 'GET', ...options }),

  post: <T = any>(endpoint: string, data?: any, options?: ApiOptions) =>
    apiRequest<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
      ...options
    }),

  put: <T = any>(endpoint: string, data?: any, options?: ApiOptions) =>
    apiRequest<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
      ...options
    }),

  patch: <T = any>(endpoint: string, data?: any, options?: ApiOptions) =>
    apiRequest<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
      ...options
    }),

  delete: <T = any>(endpoint: string, options?: ApiOptions) =>
    apiRequest<T>(endpoint, { method: 'DELETE', ...options }),
};
