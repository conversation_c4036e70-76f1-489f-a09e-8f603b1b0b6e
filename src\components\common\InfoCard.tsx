import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>ooter, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";

interface InfoCardProps {
  /**
   * Tiêu đề của card
   */
  title: string;
  
  /**
   * Giá trị chính hiển thị
   */
  value: React.ReactNode;
  
  /**
   * Icon hiển thị
   */
  icon?: React.ReactNode;
  
  /**
   * Màu của icon
   */
  iconColor?: string;
  
  /**
   * Nội dung footer
   */
  footer?: React.ReactNode;
  
  /**
   * Mô tả
   */
  description?: React.ReactNode;
  
  /**
   * CSS class bổ sung
   */
  className?: string;
  
  /**
   * Trạng thái loading
   * @default false
   */
  loading?: boolean;
  
  /**
   * Callback khi click vào card
   */
  onClick?: () => void;
  
  /**
   * <PERSON><PERSON> hiển thị border không
   * @default true
   */
  withBorder?: boolean;
  
  /**
   * <PERSON><PERSON> hiển thị shadow không
   * @default false
   */
  withShadow?: boolean;
  
  /**
   * <PERSON><PERSON> hiển thị hover effect không
   * @default false
   */
  withHoverEffect?: boolean;
}

export function InfoCard({
  title,
  value,
  icon,
  iconColor,
  footer,
  description,
  className,
  loading = false,
  onClick,
  withBorder = true,
  withShadow = false,
  withHoverEffect = false
}: InfoCardProps) {
  const cardClasses = cn(
    withBorder ? "border" : "border-0",
    withShadow ? "shadow-md" : "",
    withHoverEffect ? "transition-all duration-200 hover:shadow-md hover:border-primary/50" : "",
    onClick ? "cursor-pointer" : "",
    className
  );
  
  if (loading) {
    return (
      <Card className={cardClasses}>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <Skeleton className="h-4 w-24" />
            {icon && <Skeleton className="h-8 w-8 rounded-full" />}
          </div>
        </CardHeader>
        <CardContent>
          <Skeleton className="h-8 w-20 mb-2" />
          {description && <Skeleton className="h-4 w-32" />}
        </CardContent>
        {footer && (
          <CardFooter className="pt-0 border-t">
            <Skeleton className="h-4 w-full" />
          </CardFooter>
        )}
      </Card>
    );
  }
  
  return (
    <Card className={cardClasses} onClick={onClick}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            {title}
          </CardTitle>
          {icon && (
            <div 
              className={cn(
                "p-2 rounded-full",
                iconColor ? `text-${iconColor}-500 bg-${iconColor}-50` : "text-primary bg-primary/10"
              )}
            >
              {icon}
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {description && (
          <p className="text-xs text-muted-foreground mt-1">
            {description}
          </p>
        )}
      </CardContent>
      {footer && (
        <CardFooter className="pt-2 border-t text-xs text-muted-foreground">
          {footer}
        </CardFooter>
      )}
    </Card>
  );
}
