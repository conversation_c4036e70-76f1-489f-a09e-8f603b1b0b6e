import { Route, Navigate } from "react-router-dom";

// Auth Pages
import LoginPage from "@/pages/auth/Login";
import AuthPage from "@/pages/auth/Auth";
import ForgotPasswordPage from "@/pages/auth/ForgotPassword";
import ResetPasswordPage from "@/pages/auth/ResetPassword";

export const AuthRoutes = [
  // Redirect from /login to /auth for backward compatibility
  <Route key="login-redirect" path="/login" element={<Navigate to="/auth" replace />} />,

  // Auth routes with nested structure
  <Route key="auth" path="/auth" element={<AuthPage />}>
    <Route index element={<LoginPage />} />
    <Route path="forgot-password" element={<ForgotPasswordPage />} />
    <Route path="reset-password/:token" element={<ResetPasswordPage />} />
  </Route>,

  // Legacy routes for backward compatibility
  <Route 
    key="forgot-password-redirect" 
    path="/forgot-password" 
    element={<Navigate to="/auth/forgot-password" replace />} 
  />,
  <Route 
    key="reset-password-redirect" 
    path="/reset-password/:token" 
    element={<Navigate to={window.location.pathname.replace('/reset-password/', '/auth/reset-password/')} replace />} 
  />,
];
