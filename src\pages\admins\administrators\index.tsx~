import {useState, useEffect} from "react";
import {useForm} from "react-hook-form";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {But<PERSON>} from "@/components/ui/button";
import {Input} from "@/components/ui/input";
import {
  Search,
  Plus,
  Edit,
  Trash2,
  UserCog,
  Loader2,
  Mail,
  Phone,
  User as UserIcon,
  Lock,
  Eye,
  EyeOff
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {toast} from "sonner";
import {userService, User, PaginatedResult, CreateUserData, UpdateUserData} from "@/services/UserService";

// Interface cho form thêm/sửa người dùng
interface UserFormValues {
  fullName: string;
  email: string;
  password?: string;
  phone?: string;
  gender?: string;
  role: string;
  status: string;
}

// Component UserFormDialog
function UserFormDialog({
  open,
  onOpenChange,
  user,
  onSuccess
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  user?: User;
  onSuccess: () => void;
}) {
  const isEditing = !!user;
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<UserFormValues>({
    defaultValues: {
      fullName: user?.fullName || "",
      email: user?.email || "",
      password: "",
      phone: user?.phone || "",
      gender: user?.gender || "",
      role: user?.role || "admin",
      status: user?.status || "active"
    }
  });

  // Reset form khi mở dialog với user khác
  useEffect(() => {
    if (open) {
      form.reset({
        fullName: user?.fullName || "",
        email: user?.email || "",
        password: "",
        phone: user?.phone || "",
        gender: user?.gender || "",
        role: user?.role || "admin",
        status: user?.status || "active"
      });
    }
  }, [open, user, form]);

  const onSubmit = async (data: UserFormValues) => {
    try {
      setIsSubmitting(true);

      if (isEditing && user?._id) {
        // Cập nhật người dùng
        const updateData: UpdateUserData = {
          fullName: data.fullName,
          email: data.email,
          phone: data.phone,
          gender: data.gender,
          role: data.role,
          status: data.status
        };

        // Chỉ gửi password nếu có nhập
        if (data.password) {
          updateData.password = data.password;
        }

        await userService.updateUser(user._id, updateData);
        toast.success("Cập nhật quản trị viên thành công!");
      } else {
        // Tạo mới người dùng
        const createData: CreateUserData = {
          fullName: data.fullName,
          email: data.email,
          password: data.password || "123456", // Mật khẩu mặc định nếu không nhập
          phone: data.phone,
          gender: data.gender,
          role: data.role,
          status: data.status
        };

        await userService.createUser(createData);
        toast.success("Thêm quản trị viên thành công!");
      }

      // Đóng dialog và refresh danh sách
      onOpenChange(false);
      onSuccess();
    } catch (error: any) {
      toast.error(error.message || "Đã xảy ra lỗi khi lưu dữ liệu!");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Chỉnh sửa quản trị viên" : "Thêm mới quản trị viên"}</DialogTitle>
          <DialogDescription>
            {isEditing
              ? "Cập nhật thông tin quản trị viên trong hệ thống."
              : "Thêm mới quản trị viên vào hệ thống."}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="fullName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Họ và tên <span className="text-red-500">*</span></FormLabel>
                  <div className="relative">
                    <UserIcon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <FormControl>
                      <Input className="pl-10" placeholder="Nguyễn Văn A" {...field} required />
                    </FormControl>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email <span className="text-red-500">*</span></FormLabel>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <FormControl>
                      <Input
                        className="pl-10"
                        type="email"
                        placeholder="<EMAIL>"
                        {...field}
                        required
                        readOnly={isEditing} // Không cho phép sửa email khi đang chỉnh sửa
                        disabled={isEditing}
                      />
                    </FormControl>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{isEditing ? "Mật khẩu mới (để trống nếu không đổi)" : "Mật khẩu"} {!isEditing && <span className="text-red-500">*</span>}</FormLabel>
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <FormControl>
                      <Input
                        className="pl-10 pr-10"
                        type={showPassword ? "text" : "password"}
                        placeholder={isEditing ? "Nhập mật khẩu mới" : "Nhập mật khẩu"}
                        {...field}
                        required={!isEditing}
                      />
                    </FormControl>
                    <button
                      type="button"
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                  <FormDescription>
                    {isEditing
                      ? "Để trống nếu không muốn thay đổi mật khẩu."
                      : "Mật khẩu phải có ít nhất 6 ký tự."}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Số điện thoại</FormLabel>
                    <div className="relative">
                      <Phone className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      <FormControl>
                        <Input className="pl-10" placeholder="0912345678" {...field} />
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="gender"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Giới tính</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Chọn giới tính" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="male">Nam</SelectItem>
                        <SelectItem value="female">Nữ</SelectItem>
                        <SelectItem value="other">Khác</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Vai trò <span className="text-red-500">*</span></FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value} required>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Chọn vai trò" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="admin">Quản trị viên</SelectItem>
                        <SelectItem value="manager">Quản lý</SelectItem>
                        <SelectItem value="user">Người dùng</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Trạng thái <span className="text-red-500">*</span></FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value} required>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Chọn trạng thái" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="active">Hoạt động</SelectItem>
                        <SelectItem value="inactive">Không hoạt động</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter className="mt-6">
              <DialogClose asChild>
                <Button type="button" variant="outline">Hủy</Button>
              </DialogClose>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isEditing ? "Cập nhật" : "Thêm mới"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

export default function Administrators() {
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(true);
  const [administrators, setAdministrators] = useState<User[]>([]);
  const [paginationInfo, setPaginationInfo] = useState({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0
  });

  // State cho dialog
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | undefined>(undefined);

  // State cho dialog xác nhận xóa
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | undefined>(undefined);
  const [isDeleting, setIsDeleting] = useState(false);
  console.log("administrators", administrators);
  // Lấy danh sách quản trị viên từ API
  useEffect(() => {
    const fetchAdministrators = async () => {
      try {
        setLoading(true);
        const response = await userService.getAllUsers({
          query: searchQuery,
          page: paginationInfo.page,
          limit: paginationInfo.limit,
          role: "admin" // Lọc theo vai trò admin
        });
        // Đảm bảo response.data là một mảng
        if (response && Array.isArray(response.rows)) {
          setAdministrators(response.rows);
          setPaginationInfo({
            total: response.total || 0,
            page: response.page || 1,
            limit: response.limit || 10,
            totalPages: response.totalPages || 1
          });
        } else {
          console.error("Invalid response format:", response);
          setAdministrators([]);
        }
      } catch (error) {
        console.error("Error fetching administrators:", error);
        setAdministrators([]);
      } finally {
        setLoading(false);
      }
    };

    // Gọi hàm fetch dữ liệu
    fetchAdministrators();
  }, [searchQuery, paginationInfo.page, paginationInfo.limit]);

  // Xử lý tìm kiếm với debounce
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Hàm mở dialog thêm mới
  const handleAddNew = () => {
    setSelectedUser(undefined);
    setDialogOpen(true);
  };

  // Hàm mở dialog chỉnh sửa
  const handleEdit = (user: User) => {
    setSelectedUser(user);
    setDialogOpen(true);
  };

  // Hàm mở dialog xác nhận xóa
  const handleDeleteConfirm = (user: User) => {
    setUserToDelete(user);
    setDeleteDialogOpen(true);
  };

  // Hàm xóa người dùng
  const handleDelete = async () => {
    if (!userToDelete) return;

    try {
      setIsDeleting(true);
      await userService.deleteUser(userToDelete._id);
      toast.success("Xóa quản trị viên thành công!");

      // Cập nhật lại danh sách
      handleUserFormSuccess();
    } catch (error: any) {
      toast.error(error.message || "Đã xảy ra lỗi khi xóa quản trị viên!");
    } finally {
      setIsDeleting(false);
      setDeleteDialogOpen(false);
      setUserToDelete(undefined);
    }
  };

  // Hàm refresh danh sách sau khi thêm/sửa
  const handleUserFormSuccess = () => {
    // Gọi lại API để lấy danh sách mới nhất
    const fetchAdministrators = async () => {
      try {
        setLoading(true);
        const response = await userService.getAllUsers({
          query: searchQuery,
          page: paginationInfo.page,
          limit: paginationInfo.limit,
          role: "admin" // Lọc theo vai trò admin
        });

        if (response && Array.isArray(response.rows)) {
          setAdministrators(response.rows);
          setPaginationInfo({
            total: response.total || 0,
            page: response.page || 1,
            limit: response.limit || 10,
            totalPages: response.totalPages || 1
          });
        }
      } catch (error) {
        console.error("Error fetching administrators:", error);
        toast.error("Không thể tải danh sách quản trị viên");
      } finally {
        setLoading(false);
      }
    };

    fetchAdministrators();
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Quản trị viên</h1>
          <p className="text-muted-foreground">
            Quản lý danh sách quản trị viên trong hệ thống
          </p>
        </div>
        <Button className="flex items-center gap-2" onClick={handleAddNew}>
          <Plus className="h-4 w-4"/>
          <span>Thêm mới</span>
        </Button>
      </div>

      {/* Thanh tìm kiếm */}
      <div className="flex items-center gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"/>
          <Input
            type="search"
            placeholder="Tìm kiếm quản trị viên..."
            className="pl-8"
            value={searchQuery}
            onChange={handleSearch}
          />
        </div>
      </div>

      {/* Bảng danh sách quản trị viên */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]">STT</TableHead>
              <TableHead>Tên</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Vai trò</TableHead>
              <TableHead>Trạng thái</TableHead>
              <TableHead className="text-right">Thao tác</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-10">
                  <div className="flex justify-center items-center">
                    <Loader2 className="h-6 w-6 animate-spin mr-2"/>
                    <span>Đang tải dữ liệu...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : administrators && administrators.length > 0 ? (
              administrators.map((admin, index) => (
                <TableRow key={admin._id}>
                  <TableCell>{(paginationInfo.page - 1) * paginationInfo.limit + index + 1}</TableCell>
                  <TableCell className="font-medium">{admin.fullName}</TableCell>
                  <TableCell>{admin.email}</TableCell>
                  <TableCell>{admin.role}</TableCell>
                  <TableCell>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        admin.status === "active"
                          ? "bg-green-100 text-green-800"
                          : "bg-gray-100 text-gray-800"
                      }`}>
                        {admin.status === "active" ? "Hoạt động" : "Không hoạt động"}
                      </span>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEdit(admin)}
                        title="Chỉnh sửa"
                      >
                        <Edit className="h-4 w-4"/>
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        title="Xóa"
                        onClick={() => handleDeleteConfirm(admin)}
                      >
                        <Trash2 className="h-4 w-4 text-destructive"/>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-10 text-muted-foreground">
                  Không tìm thấy quản trị viên nào
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Phân trang */}
      {!loading && administrators && administrators.length > 0 && (
        <div className="flex items-center justify-between mt-4">
          <div className="text-sm text-muted-foreground">
            Hiển thị {administrators.length} trên tổng số {paginationInfo.total} quản trị viên
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPaginationInfo(prev => ({...prev, page: Math.max(1, prev.page - 1)}))}
              disabled={paginationInfo.page <= 1}
            >
              Trước
            </Button>
            <div className="text-sm">
              Trang {paginationInfo.page} / {paginationInfo.totalPages}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPaginationInfo(prev => ({...prev, page: Math.min(prev.totalPages, prev.page + 1)}))}
              disabled={paginationInfo.page >= paginationInfo.totalPages}
            >
              Sau
            </Button>
          </div>
        </div>
      )}

      {/* Dialog thêm/sửa quản trị viên */}
      <UserFormDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        user={selectedUser}
        onSuccess={handleUserFormSuccess}
      />

      {/* Dialog xác nhận xóa */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận xóa quản trị viên</AlertDialogTitle>
            <AlertDialogDescription>
              Bạn có chắc chắn muốn xóa quản trị viên {userToDelete?.fullName} ({userToDelete?.email}) không?
              <br />
              Hành động này không thể hoàn tác.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Hủy</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isDeleting ? "Đang xóa..." : "Xóa"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
