# Helpdesk Harmony Frontend

## Project info

A modern helpdesk system frontend built with React and TypeScript.

## How can I edit this code?

There are several ways of editing this application.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

## Environment Variables

This project uses environment variables for configuration. The variables are stored in the following files:

- `.env`: Common variables for all environments
- `.env.development`: Variables for development environment
- `.env.production`: Variables for production environment

To customize the configuration, you can create a `.env.local` file which will override the default values:

```sh
# API Backend
VITE_API_BACKEND_URL=http://your-backend-url

# SIP Config
VITE_SIP_SERVER_URL=your-sip-server
VITE_SIP_WEBSOCKET_URL=ws://your-websocket-url

# App Info
VITE_APP_NAME=Your App Name
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

You can deploy this project using any static site hosting service like Vercel, Netlify, or GitHub Pages.

```sh
# Build the project
npm run build

# The build output will be in the dist/ directory
```
