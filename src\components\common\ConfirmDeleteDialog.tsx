import { useState } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Loader2, CheckCircle, XCircle } from "lucide-react";
import { enhancedToast } from "./EnhancedToast";

interface ConfirmDeleteDialogProps {
  /**
   * Trạng thái mở của dialog
   */
  open: boolean;

  /**
   * Callback khi thay đổi trạng thái mở
   */
  onOpenChange: (open: boolean) => void;

  /**
   * Tiêu đề của dialog
   * @default "Xác nhận xóa"
   */
  title?: string;

  /**
   * Nội dung mô tả của dialog
   */
  description: React.ReactNode;

  /**
   * Callback khi xác nhận xóa
   */
  onConfirm: () => Promise<void>;

  /**
   * Thông báo thành công
   * @default "Xóa thành công!"
   */
  successMessage?: string;

  /**
   * Thông báo lỗi
   * @default "Đã xảy ra lỗi khi xóa!"
   */
  errorMessage?: string;

  /**
   * Text của nút xác nhận
   * @default "Xóa"
   */
  confirmText?: string;

  /**
   * Text của nút hủy
   * @default "Hủy"
   */
  cancelText?: string;

  /**
   * Text khi đang xóa
   * @default "Đang xóa..."
   */
  deletingText?: string;
}

export function ConfirmDeleteDialog({
  open,
  onOpenChange,
  title = "Xác nhận xóa",
  description,
  onConfirm,
  successMessage = "Xóa thành công!",
  errorMessage = "Đã xảy ra lỗi khi xóa!",
  confirmText = "Xóa",
  cancelText = "Hủy",
  deletingText = "Đang xóa..."
}: ConfirmDeleteDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleConfirm = async () => {
    try {
      setIsDeleting(true);
      await onConfirm();
      enhancedToast.success(successMessage, {
        id: `delete-success-${Date.now()}`,
        icon: <CheckCircle className="h-5 w-5" />,
        duration: 4000,
        closeButton: true
      });
    } catch (error: any) {
      enhancedToast.error(error?.message || errorMessage, {
        id: `delete-error-${Date.now()}`,
        icon: <XCircle className="h-5 w-5" />,
        duration: 5000,
        closeButton: true
      });
    } finally {
      setIsDeleting(false);
      onOpenChange(false);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription>
            {description}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>{cancelText}</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={isDeleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isDeleting ? deletingText : confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
