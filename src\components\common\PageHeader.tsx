import React from "react";
import { Button } from "@/components/ui/button";

interface PageHeaderProps {
  /**
   * Ti<PERSON>u đề của trang
   */
  title: string;
  
  /**
   * <PERSON><PERSON> tả của trang
   */
  description?: string;
  
  /**
   * C<PERSON><PERSON> nút hành động
   */
  actions?: React.ReactNode;
  
  /**
   * CSS class bổ sung
   */
  className?: string;
  
  /**
   * Nội dung bổ sung
   */
  children?: React.ReactNode;
}

export function PageHeader({
  title,
  description,
  actions,
  className = "",
  children
}: PageHeaderProps) {
  return (
    <div className={`flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 ${className}`}>
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
        {description && (
          <p className="text-muted-foreground">
            {description}
          </p>
        )}
        {children}
      </div>
      
      {actions && (
        <div className="flex items-center gap-2 mt-2 sm:mt-0">
          {actions}
        </div>
      )}
    </div>
  );
}

interface PageHeaderActionProps {
  /**
   * Icon của nút
   */
  icon?: React.ReactNode;
  
  /**
   * Text của nút
   */
  text: string;
  
  /**
   * Callback khi click
   */
  onClick?: () => void;
  
  /**
   * Variant của nút
   * @default "default"
   */
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  
  /**
   * CSS class bổ sung
   */
  className?: string;
}

export function PageHeaderAction({
  icon,
  text,
  onClick,
  variant = "default",
  className = ""
}: PageHeaderActionProps) {
  return (
    <Button 
      className={`flex items-center gap-2 ${className}`} 
      onClick={onClick}
      variant={variant}
    >
      {icon}
      <span>{text}</span>
    </Button>
  );
}
