import React, {useState, useCallback} from "react";
import <PERSON><PERSON><PERSON> from "react-easy-crop";
import {But<PERSON>} from "@/components/ui/button";
import {Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter} from "@/components/ui/dialog";
import {Slider} from "@/components/ui/slider";
import {RotateCw, ZoomIn} from "lucide-react";

// Hàm tạo canvas để cắt ảnh
const createImage = (url: string): Promise<HTMLImageElement> =>
  new Promise((resolve, reject) => {
    const image = new Image();
    image.addEventListener("load", () => resolve(image));
    image.addEventListener("error", (error) => reject(error));
    image.setAttribute("crossOrigin", "anonymous");
    image.src = url;
  });

// Hàm lấy phần ảnh đã crop
async function getCroppedImg(
  imageSrc: string,
  pixelCrop: { x: number; y: number; width: number; height: number },
  rotation = 0
): Promise<Blob> {
  const image = await createImage(imageSrc);
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");

  if (!ctx) {
    throw new Error("Canvas context is not available");
  }

  const maxSize = Math.max(image.width, image.height);
  const safeArea = 2 * ((maxSize / 2) * Math.sqrt(2));

  // Đặt kích thước canvas để xử lý xoay ảnh
  canvas.width = safeArea;
  canvas.height = safeArea;

  // Vẽ ảnh vào giữa canvas
  ctx.translate(safeArea / 2, safeArea / 2);
  ctx.rotate((rotation * Math.PI) / 180);
  ctx.translate(-safeArea / 2, -safeArea / 2);
  ctx.drawImage(
    image,
    safeArea / 2 - image.width / 2,
    safeArea / 2 - image.height / 2
  );

  // Lấy dữ liệu từ canvas
  const data = ctx.getImageData(0, 0, safeArea, safeArea);

  // Đặt kích thước canvas thành kích thước crop
  canvas.width = pixelCrop.width;
  canvas.height = pixelCrop.height;

  // Vẽ phần đã crop vào canvas mới
  ctx.putImageData(
    data,
    Math.round(0 - safeArea / 2 + image.width / 2 - pixelCrop.x),
    Math.round(0 - safeArea / 2 + image.height / 2 - pixelCrop.y)
  );

  // Chuyển canvas thành blob
  return new Promise((resolve) => {
    canvas.toBlob((blob) => {
      if (blob) {
        resolve(blob);
      }
    }, "image/jpeg");
  });
}

interface ImageCropperProps {
  /**
   * Ảnh cần crop
   */
  image: string;

  /**
   * Callback khi hoàn thành crop
   */
  onCropComplete: (croppedImage: Blob) => void;

  /**
   * Callback khi hủy crop
   */
  onCancel: () => void;

  /**
   * Trạng thái mở của dialog
   */
  open: boolean;

  /**
   * Tỷ lệ khung hình
   * @default 1 (hình vuông)
   */
  aspect?: number;

  /**
   * Hình dạng crop
   * @default "round"
   */
  cropShape?: "rect" | "round";
}

/**
 * Component crop ảnh
 */
export function ImageCropper({
                               image,
                               onCropComplete,
                               onCancel,
                               open,
                               aspect = 1,
                               cropShape = "round"
                             }: ImageCropperProps) {
  const [crop, setCrop] = useState({x: 0, y: 0});
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<{
    x: number;
    y: number;
    width: number;
    height: number;
  } | null>(null);

  const handleCropComplete = useCallback(
    (_croppedArea: any, croppedAreaPixels: any) => {
      setCroppedAreaPixels(croppedAreaPixels);
    },
    []
  );

  const handleCrop = async () => {
    if (!croppedAreaPixels) return;

    try {
      const croppedImage = await getCroppedImg(
        image,
        croppedAreaPixels,
        rotation
      );
      onCropComplete(croppedImage);
    } catch (e) {
      console.error("Lỗi khi crop ảnh:", e);
    }
  };

  return (
    <Dialog open={open} onOpenChange={(open) => !open && onCancel()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Chỉnh sửa ảnh đại diện</DialogTitle>
        </DialogHeader>

        <div className="relative w-full h-[300px] mt-4">
          <Cropper
            image={image}
            crop={crop}
            zoom={zoom}
            aspect={aspect}
            rotation={rotation}
            onCropChange={setCrop}
            onCropComplete={handleCropComplete}
            onZoomChange={setZoom}
            cropShape={cropShape}
            showGrid={true}
          />
        </div>

        <div className="space-y-4 mt-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">Phóng to</label>
              <ZoomIn className="h-4 w-4 text-muted-foreground"/>
            </div>
            <Slider
              value={[zoom]}
              min={1}
              max={3}
              step={0.1}
              onValueChange={(value) => setZoom(value[0])}
            />
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">Xoay</label>
              <RotateCw className="h-4 w-4 text-muted-foreground"/>
            </div>
            <Slider
              value={[rotation]}
              min={0}
              max={360}
              step={1}
              onValueChange={(value) => setRotation(value[0])}
            />
          </div>
        </div>

        <DialogFooter className="flex justify-between sm:justify-between">
          <Button variant="outline" onClick={onCancel}>
            Hủy
          </Button>
          <Button onClick={handleCrop}>
            Áp dụng
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
