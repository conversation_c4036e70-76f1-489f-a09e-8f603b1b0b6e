import {useEffect, useRef, useState} from "react";
import {io} from "socket.io-client";

let socketInstance = null;

export default function useSocket(user_id) {
  const [socketConnected, setSocketConnected] = useState(false);
  const socket = useRef(null);

  useEffect(() => {

    if (!socketInstance) {
      socketInstance = io("/chat-cskh", {
        path: "/socket.io/socket.io",
        transports: ["websocket"],
        withCredentials: true,
        // query: {verify: true},
      });
    }

    socket.current = socketInstance;

    const handleConnect = () => {
      setSocketConnected(true);
      socketInstance.emit("join_workspace", {user_id});
    };

    const handleDisconnect = () => {
      setSocketConnected(false);
    };

    // 🔁 Luôn gắn lại listener (dù socket đã tồn tại)
    socketInstance.on("connect", handleConnect);
    socketInstance.on("disconnect", handleDisconnect);

    // Nếu socket đã connect từ trước, cập nhật state ngay
    if (socketInstance.connected) {
      handleConnect();
    }

    return () => {
      socketInstance.off("connect", handleConnect);
      socketInstance.off("disconnect", handleDisconnect);
      socketInstance.disconnect();
      socketInstance = null;
    };
  }, [user_id]);


  return {socket: socket.current, socketConnected};
}
