import { CustomerLayout } from "@/components/layout/CustomerLayout";
import { SEO } from "@/components/SEO";
import { useAuth } from "@/contexts/AuthContext";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import {
  ArrowRight,
  Headphones,
  MessageCircle,
  FileText
} from "lucide-react";

export default function CustomerDashboard() {
  const { user } = useAuth();
  const navigate = useNavigate();

  const userRole = user?.role || 'shop';
  const basePath = userRole === 'buyer' ? '/buyer' : '/shop';
  const userTitle = userRole === 'buyer' ? 'Người mua' : 'Cửa hàng';

  // Main services - soft and gentle
  const services = [
    {
      id: 'call',
      title: 'Gọi CSKH',
      description: 'Liên hệ trực tiếp qua điện thoại',
      icon: <Headphones className="h-10 w-10" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-100',
      hoverBg: 'hover:bg-blue-100',
      path: `${basePath}/call`
    },
    {
      id: 'chat',
      title: 'Chat với CSKH',
      description: 'Trò chuyện qua tin nhắn',
      icon: <MessageCircle className="h-10 w-10" />,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-100',
      hoverBg: 'hover:bg-green-100',
      path: `${basePath}/support`
    },
    {
      id: 'history',
      title: 'Lịch sử đơn hàng',
      description: 'Xem đơn hàng và hỗ trợ',
      icon: <FileText className="h-10 w-10" />,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-100',
      hoverBg: 'hover:bg-purple-100',
      path: `${basePath}/history`
    }
  ];

  return (
    <CustomerLayout>
      <SEO
        title={`Trang chính - ${userTitle}`}
        description="Trang chính dành cho khách hàng - Hỗ trợ CSKH"
      />

      <div className="min-h-[calc(100vh-200px)] flex flex-col">
        {/* Welcome Section */}
        <div className="text-center py-16">
          <h1 className="text-3xl font-medium text-foreground mb-3">
            Chào mừng đến với GHVN CSKH
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
            Chúng tôi luôn sẵn sàng hỗ trợ bạn. Chọn cách thức liên hệ phù hợp nhất.
          </p>
        </div>

        {/* Main Services */}
        <div className="flex-1 flex items-center justify-center">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full max-w-5xl">
            {services.map((service) => (
              <Card
                key={service.id}
                className={`group cursor-pointer ${service.bgColor} ${service.borderColor} border-2 ${service.hoverBg} transition-all duration-300 hover:shadow-md`}
                onClick={() => navigate(service.path)}
              >
                <CardContent className="p-8 text-center">
                  <div className={`mx-auto w-16 h-16 rounded-full bg-white flex items-center justify-center mb-6 group-hover:scale-105 transition-transform duration-300 shadow-sm`}>
                    <div className={service.color}>
                      {service.icon}
                    </div>
                  </div>

                  <h3 className="text-xl font-semibold text-foreground mb-3">
                    {service.title}
                  </h3>

                  <p className="text-muted-foreground mb-6 leading-relaxed text-sm">
                    {service.description}
                  </p>

                  <Button
                    variant="outline"
                    className={`w-full ${service.borderColor} ${service.color} hover:bg-white border-2 py-2 font-medium group-hover:shadow-sm transition-all duration-300`}
                  >
                    Bắt đầu
                    <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Emergency Contact */}
        <div className="text-center py-12">
          <div className="inline-flex items-center gap-3 bg-orange-50 border border-orange-200 rounded-full px-5 py-2">
            <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse" />
            <span className="text-orange-700 text-sm font-medium">
              Khẩn cấp? Gọi ngay:
              <Button
                variant="link"
                className="text-orange-700 font-semibold p-0 ml-1"
                onClick={() => navigate(`${basePath}/call`)}
              >
                1900-xxxx
              </Button>
            </span>
          </div>
        </div>
      </div>
    </CustomerLayout>
  );
}
