import { useState, useEffect, useCallback } from 'react';
import sipService, { CallState, CallStatus } from '../services/sipService';
import { ringToneService } from '../services/ringToneService';

interface UseSipCallProps {
    autoConnect?: boolean;
    username?: string;
    password?: string;
}

export const useSipCall = ({
    autoConnect = true,
    username,
    password
}: UseSipCallProps = {}) => {
    const [callState, setCallState] = useState<CallState>({ status: CallStatus.IDLE });
    const [isInitialized, setIsInitialized] = useState(false);
    const [isMuted, setIsMuted] = useState(false);
    const [isRinging, setIsRinging] = useState(false);

    // Khởi tạo SIP service
    const initialize = useCallback(async (user?: string, pass?: string) => {
        try {
            await sipService.initialize(user || username, pass || password);
            setIsInitialized(true);
        } catch (error) {
            console.error('Failed to initialize SIP service', error);
        }
    }, [username, password]);

    // Thực hiện cuộc gọi đi
    const makeCall = useCallback(async (destination: string, options?: { skipMediaAccess?: boolean }) => {
        if (!isInitialized) {
            throw new Error('SIP service not initialized');
        }
        await sipService.makeCall(destination, options);
    }, [isInitialized]);

    // Trả lời cuộc gọi đến
    const answerCall = useCallback(async (options?: { skipMediaAccess?: boolean }) => {
        if (!isInitialized) {
            throw new Error('SIP service not initialized');
        }
        await sipService.answerCall(options);
    }, [isInitialized]);

    // Từ chối cuộc gọi đến
    const rejectCall = useCallback(async () => {
        if (!isInitialized) {
            throw new Error('SIP service not initialized');
        }
        await sipService.rejectCall();
    }, [isInitialized]);

    // Kết thúc cuộc gọi hiện tại
    const hangupCall = useCallback(async () => {
        if (!isInitialized) {
            throw new Error('SIP service not initialized');
        }
        await sipService.hangupCall();
    }, [isInitialized]);

    // Bắt đầu ghi âm cuộc gọi
    const startRecording = useCallback(() => {
        if (!isInitialized) {
            console.warn('Không thể bắt đầu ghi âm: SIP service chưa được khởi tạo');
            return;
        }

        try {
            sipService.startRecording();
        } catch (error) {
            console.error('Lỗi khi bắt đầu ghi âm:', error);
        }
    }, [isInitialized]);

    // Dừng ghi âm cuộc gọi
    const stopRecording = useCallback(() => {
        if (!isInitialized) {
            console.warn('Không thể dừng ghi âm: SIP service chưa được khởi tạo');
            return;
        }

        try {
            sipService.stopRecording();
        } catch (error) {
            console.error('Lỗi khi dừng ghi âm:', error);
        }
    }, [isInitialized]);

    // Tắt/bật microphone
    const toggleMute = useCallback(() => {
        if (!isInitialized) {
            console.warn('Không thể tắt/bật microphone: SIP service chưa được khởi tạo');
            return;
        }

        try {
            const newMuteState = !isMuted;
            sipService.toggleMute(newMuteState);
            setIsMuted(newMuteState);
        } catch (error) {
            console.error('Lỗi khi tắt/bật microphone:', error);
        }
    }, [isInitialized, isMuted]);

    // Hủy kết nối SIP
    const disconnect = useCallback(async () => {
        if (!isInitialized) return;
        await sipService.disconnect();
        setIsInitialized(false);
    }, [isInitialized]);

    // Đăng ký callback để nhận thông báo khi trạng thái cuộc gọi thay đổi
    useEffect(() => {
        const handleCallStateChange = (state: CallState) => {
            setCallState(state);

            // Cập nhật trạng thái chuông báo
            setIsRinging(state.status === CallStatus.INCOMING && ringToneService.isCurrentlyRinging);
        };

        sipService.onCallStateChange(handleCallStateChange);

        // Tự động kết nối nếu được yêu cầu
        if (autoConnect && !isInitialized) {
            initialize();
        }

        // Cleanup khi component unmount
        return () => {
            disconnect();
        };
    }, [autoConnect, initialize, disconnect, isInitialized]);

    return {
        callState,
        isInitialized,
        isMuted,
        isRinging,
        initialize,
        makeCall,
        answerCall,
        rejectCall,
        hangupCall,
        startRecording,
        stopRecording,
        toggleMute,
        disconnect
    };
};
