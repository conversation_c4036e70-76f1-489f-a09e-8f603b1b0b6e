import {useEffect, useRef} from "react";
import {
  EditorView,
  highlightSpecialChars,
  drawSelection,
  placeholder,
  keymap,
  rectangularSelection
} from "@codemirror/view";
import {EditorState, StateEffect} from "@codemirror/state";
import {markdown} from "@codemirror/lang-markdown";
import {
  history,
  defaultKeymap,
  historyKeymap,
  indentWithTab
} from "@codemirror/commands";
import {
  indentOnInput,
  bracketMatching,
  syntaxHighlighting,
  defaultHighlightStyle,
  foldKeymap
} from "@codemirror/language";
import {
  highlightSelectionMatches,
  searchKeymap
} from "@codemirror/search";

interface CodemirrorMarkdownProps {
  disable?: boolean;
  value?: string;
  placeholder?: string;
  onChange?: (value: string) => void;
  minHeight?: number;
  maxHeight?: number;
}

export default function CodemirrorMarkdown({
                                             disable = false,
                                             value,
                                             onChange,
                                             minHeight = 200,
                                             maxHeight = 600,
                                             ...props
                                           }: CodemirrorMarkdownProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const viewRef = useRef<EditorView | null>(null);

  const customBasicSetup = [
    history(),
    drawSelection(),
    indentOnInput(),
    bracketMatching(),
    rectangularSelection(),
    highlightSpecialChars(),
    highlightSelectionMatches(),
    placeholder(props?.placeholder),
    keymap.of([
      ...defaultKeymap,
      ...searchKeymap,
      ...historyKeymap,
      ...foldKeymap,
      indentWithTab,
    ]),
    EditorState.allowMultipleSelections.of(true),
    syntaxHighlighting(defaultHighlightStyle, {fallback: true}),
  ];

  const customTheme = EditorView.theme({
    ".cm-content": {
      whiteSpace: "pre-wrap",
      padding: '10px',
      minHeight: `${minHeight}px`,
      maxHeight: `${maxHeight}px`,
      wordBreak: "break-word",
      overflowWrap: "break-word",
      maxWidth: "100%",
      boxSizing: "border-box",
      textAlign: "justify",
    },
    ".cm-line": {
      fontFamily: "system-ui, sans-serif",
      fontSize: '0.875rem',
      fontWeight: '400',
      color: 'inherit',
      letterSpacing: 'normal',
      whiteSpace: "pre-wrap !important",
      wordBreak: "break-word !important",
      overflowWrap: "break-word !important",
      maxWidth: "100% !important",
      boxSizing: "border-box",
      lineHeight: "22px",
    },
    ".cm-activeLine": {backgroundColor: "transparent !important"},
    "&.cm-editor": {outline: "none", width: "100%"},
    ".cm-scroller": {
      outline: "none",
      overflowX: "hidden",
      scrollbarWidth: "thin",
      margin: '5px 0'
    },
    ".ͼ7": {color: "#00818C", fontWeight: "bold"},
    ".ͼ5": {color: "#00818C"},
    ".cm-line .cm-placeholder": {
      fontFamily: "system-ui, sans-serif",
      fontSize: '0.875rem !important',
      fontWeight: '400 !important',
      color: "#64748b !important",
      letterSpacing: '0 !important',
      lineHeight: '1.25rem !important',
    }
  });

  useEffect(() => {
    if (!editorRef.current || viewRef.current) return;

    const view = new EditorView({
      state: EditorState.create({
        doc: value || "",
        extensions: [
          customBasicSetup,
          markdown(),
          customTheme,
          EditorView.editable.of(!disable),
          EditorView.updateListener.of((update) => {
            if (update.docChanged) {
              const text = update.state.doc.toString();
              onChange?.(text);
            }
          }),
        ],
      }),
      parent: editorRef.current,
    });

    viewRef.current = view;

    return () => {
      view.destroy();
      viewRef.current = null;
    };
  }, []);

  useEffect(() => {
    if (!viewRef.current) return;
    viewRef.current.dispatch({
      effects: StateEffect.reconfigure.of([
        customBasicSetup,
        markdown(),
        customTheme,
        EditorView.editable.of(!disable),
        EditorView.updateListener.of((update) => {
          if (update.docChanged) {
            const text = update.state.doc.toString();
            onChange?.(text);
          }
        }),
      ]),
    });
  }, [disable]);

  useEffect(() => {
    if (!viewRef.current || typeof value !== "string") return;
    const currentText = viewRef.current.state.doc.toString();
    if (value !== currentText) {
      viewRef.current.dispatch({
        changes: {from: 0, to: currentText.length, insert: value},
      });
    }
  }, [value]);
  return <div
    className="border rounded-md border-input focus-within:border-purple-500 bg-white "
  >
    <div ref={editorRef}/>
  </div>;
}
