
import { Loader2 } from "lucide-react";

export const LoadingScreen = () => {
  return (
    <div className="h-screen w-full flex flex-col items-center justify-center bg-background">
      <div className="app-logo rounded-md bg-brand-orange p-2 mb-4">
        <h1 className="font-bold text-white text-2xl">GHVN</h1>
      </div>
      <Loader2 className="h-12 w-12 animate-spin text-primary" />
      <p className="mt-4 text-lg text-muted-foreground"><PERSON><PERSON> tải...</p>
    </div>
  );
};
