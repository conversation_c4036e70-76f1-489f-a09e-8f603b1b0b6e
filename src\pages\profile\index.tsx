import {useState, useRef} from "react";
import {useForm} from "react-hook-form";
import {DefaultLayout} from "@/components/layout/DefaultLayout";
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, Ta<PERSON>Content} from "@/components/ui/tabs";
import {Card, CardContent} from "@/components/ui/card";
import {Button} from "@/components/ui/button";
import {Input} from "@/components/ui/input";
import {Label} from "@/components/ui/label";
import {Eye, EyeOff, Loader2, CheckCircle, XCircle, Camera} from "lucide-react";
import {useAuth} from "@/contexts/AuthContext";
import {SEO} from "@/components/SEO";
import {fileUploadService, UploadProgressCallback} from "@/services/FileService.ts";
import {userService} from "@/services/UserService";
import {enhancedToast} from "@/components/common/EnhancedToast";
import {UploadProgress} from "@/components/common/UploadProgress";
import {ImageCropper} from "@/components/common/ImageCropper";

interface ProfileFormValues {
  fullName: string;
  email: string;
  phone: string;
  gender: string;
  status: string;
}

interface PasswordFormValues {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export default function ProfilePage() {
  const {user} = useAuth();
  const [activeTab, setActiveTab] = useState("info");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [uploadedImage, setUploadedImage] = useState<string | null>(
    user?.avatarId ? fileUploadService.getFileContentUrl(user.avatarId) : null
  );
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadStatus, setUploadStatus] = useState<"idle" | "uploading" | "success" | "error">("idle");
  const [uploadError, setUploadError] = useState<string | undefined>(undefined);
  const [showCropper, setShowCropper] = useState(false);
  const [imageToEdit, setImageToEdit] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const profileForm = useForm<ProfileFormValues>({
    defaultValues: {
      fullName: user?.fullName || "",
      email: user?.email || "",
      phone: user?.phone || "",
      gender: user?.gender || "",
    }
  });

  const passwordForm = useForm<PasswordFormValues>({
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: ""
    }
  });

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Đọc file để hiển thị trong cropper
      const reader = new FileReader();
      reader.onloadend = () => {
        setImageToEdit(reader.result as string);
        setShowCropper(true);
      };
      reader.readAsDataURL(file);

      // Reset input để có thể chọn lại cùng một file
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const handleCropComplete = async (croppedBlob: Blob) => {
    try {
      setShowCropper(false);
      setUploadStatus("uploading");
      setUploadProgress(0);

      // Tạo File từ Blob
      const file = new File([croppedBlob], "avatar.jpg", { type: "image/jpeg" });

      // Hiển thị preview tạm thời trước khi upload hoàn tất
      const reader = new FileReader();
      reader.onloadend = () => {
        setUploadedImage(reader.result as string);
      };
      reader.readAsDataURL(croppedBlob);

      // Cấu hình callbacks để theo dõi tiến trình
      const uploadCallbacks: UploadProgressCallback = {
        onProgress: (progress) => {
          setUploadProgress(progress);
        },
        onSuccess: async (result) => {
          setUploadStatus("success");

          // Cập nhật avatar trong profile
          await userService.updateProfile({
            avatarId: result._id
          });

          // Cập nhật URL hiển thị avatar từ server
          const imageUrl = fileUploadService.getFileContentUrl(result._id);
          setUploadedImage(imageUrl);

          enhancedToast.success("Ảnh đại diện đã được cập nhật", {
            icon: <CheckCircle className="h-5 w-5" />,
            duration: 4000,
            closeButton: true
          });
        },
        onError: (error) => {
          setUploadStatus("error");
          setUploadError(error.message);

          enhancedToast.error("Đã xảy ra lỗi khi tải lên ảnh đại diện", {
            icon: <XCircle className="h-5 w-5" />,
            duration: 5000,
            closeButton: true
          });
        }
      };

      // Upload file lên server với callbacks
      await fileUploadService.uploadFile(file, uploadCallbacks);
    } catch (error) {
      setUploadStatus("error");
      setUploadError("Đã xảy ra lỗi khi xử lý ảnh");
      console.error("Error processing image:", error);

      enhancedToast.error("Đã xảy ra lỗi khi xử lý ảnh", {
        icon: <XCircle className="h-5 w-5" />,
        duration: 5000,
        closeButton: true
      });
    }
  };

  const onProfileSubmit = async (data: ProfileFormValues) => {
    setIsSubmitting(true);
    try {
      // Gọi API cập nhật thông tin profile
      await userService.updateProfile({
        fullName: data.fullName,
        email: data.email,
        phone: data.phone,
        gender: data.gender,
      });

      enhancedToast.success("Thông tin tài khoản đã được cập nhật", {
        icon: <CheckCircle className="h-5 w-5" />,
        duration: 4000,
        closeButton: true
      });
    } catch (error) {
      console.error("Error updating profile:", error);
      enhancedToast.error("Đã xảy ra lỗi khi cập nhật thông tin", {
        icon: <XCircle className="h-5 w-5" />,
        duration: 5000,
        closeButton: true
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const onPasswordSubmit = async (data: PasswordFormValues) => {
    if (data.newPassword !== data.confirmPassword) {
      enhancedToast.error("Mật khẩu nhập lại không khớp", {
        icon: <XCircle className="h-5 w-5" />,
        duration: 5000,
        closeButton: true
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // Gọi API thay đổi mật khẩu
      await userService.changePassword(data.currentPassword, data.newPassword);

      enhancedToast.success("Mật khẩu đã được thay đổi thành công", {
        icon: <CheckCircle className="h-5 w-5" />,
        duration: 4000,
        closeButton: true
      });
      passwordForm.reset();
    } catch (error) {
      console.error("Error changing password:", error);
      enhancedToast.error("Đã xảy ra lỗi khi thay đổi mật khẩu", {
        icon: <XCircle className="h-5 w-5" />,
        duration: 5000,
        closeButton: true
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <DefaultLayout>
      <SEO title="Hồ sơ" description="Quản lý thông tin cá nhân và bảo mật tài khoản"/>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Hồ sơ</h1>
        </div>

        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full"
        >
          <TabsList className="grid w-full md:w-[400px] grid-cols-2">
            <TabsTrigger
              value="info"
              className="data-[state=active]:border-b-2 data-[state=active]:border-primary"
            >
              THÔNG TIN CHÍNH
            </TabsTrigger>
            <TabsTrigger
              value="security"
              className="data-[state=active]:border-b-2 data-[state=active]:border-primary"
            >
              BẢO MẬT
            </TabsTrigger>
          </TabsList>

          <TabsContent value="info" className="mt-6">
            <Card className="border-0 shadow-sm">
              <CardContent className="p-6">
                <h2 className="text-xl font-semibold mb-6 pb-2 border-b">Thông tin chính</h2>

                <form onSubmit={profileForm.handleSubmit(onProfileSubmit)} className="space-y-6">
                  {/* Profile Image */}
                  <div className="mb-8">
                    <div className="flex flex-col items-center space-y-4">
                      <div className="flex-shrink-0">
                        <div className="w-32 h-32 border rounded-full overflow-hidden relative group">
                          {uploadedImage ? (
                            <>
                              <img
                                src={uploadedImage}
                                alt="Profile"
                                className="w-full h-full object-cover"
                              />
                              <div
                                className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
                                onClick={() => fileInputRef.current?.click()}
                              >
                                <Camera className="h-6 w-6 text-white" />
                              </div>
                            </>
                          ) : (
                            <div
                              className="w-full h-full bg-gray-100 flex items-center justify-center cursor-pointer"
                              onClick={() => fileInputRef.current?.click()}
                            >
                              <Camera className="h-6 w-6 text-gray-400" />
                            </div>
                          )}
                        </div>

                        {/* Upload Progress */}
                        {uploadStatus === "uploading" && (
                          <div className="mt-2 w-32">
                            <UploadProgress
                              progress={uploadProgress}
                              status={uploadStatus}
                              error={uploadError}
                              size="sm"
                            />
                          </div>
                        )}
                      </div>
                      <div className="flex flex-col items-center space-y-2">
                        <Button
                          type="button"
                          variant="secondary"
                          className="text-sm bg-indigo-600 hover:bg-indigo-700 text-white"
                          onClick={() => fileInputRef.current?.click()}
                          disabled={uploadStatus === "uploading"}
                        >
                          {uploadStatus === "uploading" ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin"/>
                              Đang tải lên...
                            </>
                          ) : (
                            "Tải lên ảnh đại diện"
                          )}
                        </Button>
                        <input
                          ref={fileInputRef}
                          type="file"
                          accept="image/png, image/jpg, image/jpeg, image/gif, image/webp, image/bmp"
                          className="hidden"
                          onChange={handleFileSelect}
                        />
                        <p className="text-xs text-center text-muted-foreground">
                          Định dạng được phép: png, jpg, jpeg, gif, webp, bmp
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Image Cropper */}
                  {imageToEdit && (
                    <ImageCropper
                      image={imageToEdit}
                      onCropComplete={handleCropComplete}
                      onCancel={() => setShowCropper(false)}
                      open={showCropper}
                      aspect={1}
                      cropShape="round"
                    />
                  )}

                  {/* Personal Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="fullName" className="text-sm">
                        Họ và tên <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="fullName"
                        {...profileForm.register("fullName")}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-sm">
                        Email <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="email"
                        type="email"
                        {...profileForm.register("email")}
                        required
                        readOnly
                        className="bg-gray-50"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone" className="text-sm">
                        Số điện thoại <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="phone"
                        type="tel"
                        {...profileForm.register("phone")}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="gender" className="text-sm">
                        Giới tính
                      </Label>
                      <Input
                        id="gender"
                        {...profileForm.register("gender")}
                      />
                    </div>
                  </div>

                  {/* Buttons */}
                  <div className="flex justify-end gap-4 pt-4">
                    <Button type="button" variant="outline">
                      Huỷ
                    </Button>
                    <Button
                      type="submit"
                      className="bg-indigo-600 hover:bg-indigo-700"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin"/>
                          Đang xử lý...
                        </>
                      ) : (
                        "Cập nhật"
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security" className="mt-6">
            <Card className="border-0 shadow-sm">
              <CardContent className="p-6">
                <h2 className="text-xl font-semibold mb-6 pb-2 border-b">Thay đổi mật khẩu</h2>

                <form onSubmit={passwordForm.handleSubmit(onPasswordSubmit)} className="space-y-6">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="current-password" className="text-sm">
                        Mật khẩu cũ
                      </Label>
                      <div className="relative">
                        <Input
                          id="current-password"
                          type={showCurrentPassword ? "text" : "password"}
                          {...passwordForm.register("currentPassword")}
                          required
                          className="pr-10"
                        />
                        <button
                          type="button"
                          className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                          onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                        >
                          {showCurrentPassword ? <EyeOff className="h-4 w-4"/> : <Eye className="h-4 w-4"/>}
                        </button>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="new-password" className="text-sm">
                          Mật khẩu mới
                        </Label>
                        <div className="relative">
                          <Input
                            id="new-password"
                            type={showNewPassword ? "text" : "password"}
                            {...passwordForm.register("newPassword")}
                            required
                            className="pr-10"
                          />
                          <button
                            type="button"
                            className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                            onClick={() => setShowNewPassword(!showNewPassword)}
                          >
                            {showNewPassword ? <EyeOff className="h-4 w-4"/> : <Eye className="h-4 w-4"/>}
                          </button>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="confirm-password" className="text-sm">
                          Nhập lại mật khẩu
                        </Label>
                        <div className="relative">
                          <Input
                            id="confirm-password"
                            type={showConfirmPassword ? "text" : "password"}
                            {...passwordForm.register("confirmPassword")}
                            required
                            className="pr-10"
                          />
                          <button
                            type="button"
                            className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          >
                            {showConfirmPassword ? <EyeOff className="h-4 w-4"/> : <Eye className="h-4 w-4"/>}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Buttons */}
                  <div className="flex justify-end gap-4 pt-4">
                    <Button
                      type="submit"
                      className="bg-indigo-600 hover:bg-indigo-700"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin"/>
                          Đang xử lý...
                        </>
                      ) : (
                        "Thay đổi mật khẩu"
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DefaultLayout>
  );
}
