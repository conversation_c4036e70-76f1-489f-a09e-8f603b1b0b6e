import {useState} from "react";
import {NavLink, useNavigate, useLocation} from "react-router-dom";
import {Button} from "@/components/ui/button";
import {cn} from "@/lib/utils";
import {useAuth} from "@/contexts/AuthContext";
import {
  Menu,
  LayoutDashboard,
  Users,
  FileSearch,
  PhoneCall,
  MessageCircleMore,
  BotMessageSquare
} from "lucide-react";

export function Sidebar({isCollapsed, onToggle, onMobileToggle}: {
  isCollapsed?: boolean,
  onToggle?: () => void,
  onMobileToggle?: (isOpen: boolean) => void
}) {
  const {logout} = useAuth();
  const [collapsed, setCollapsed] = useState(isCollapsed || false);
  const [mobileOpen, setMobileOpen] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // Sử dụng prop isCollapsed nếu được cung cấp
  const effectiveCollapsed = isCollapsed !== undefined ? isCollapsed : collapsed;

  const toggleSidebar = () => {
    if (onToggle) {
      onToggle();
    } else {
      setCollapsed(!collapsed);
    }
  };

  const toggleMobile = () => {
    const newMobileOpen = !mobileOpen;
    setMobileOpen(newMobileOpen);
    if (onMobileToggle) {
      onMobileToggle(newMobileOpen);
    }
  };

  const closeMobile = () => {
    setMobileOpen(false);
    if (onMobileToggle) {
      onMobileToggle(false);
    }
  };

  return (
    <>
      {/* Mobile toggle button */}
      <Button
        variant="outline"
        size="icon"
        className="fixed left-1 top-4 z-40 md:hidden"
        onClick={toggleMobile}
      >
        <Menu/>
      </Button>

      {/* Mobile overlay */}
      {mobileOpen && (
        <div
          className="fixed inset-0 z-20 bg-black/50 md:hidden"
          onClick={closeMobile}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-30 flex h-screen w-64 flex-col bg-sidebar border-r border-border shadow-sm transition-all duration-300",
          // Mobile behavior
          !mobileOpen && "-translate-x-full",
          mobileOpen && "translate-x-0",
          // Desktop behavior
          "md:translate-x-0",
          effectiveCollapsed && "md:w-20",
          !effectiveCollapsed && "md:w-64"
        )}
      >
        {/* Logo */}
        <div className="flex h-16 items-center justify-center border-b border-border px-4">
          <div className="flex items-center justify-center">
            <div className={cn("app-logo rounded-md p-1", {
              "w-12 h-12": effectiveCollapsed && !mobileOpen,
              "w-24 h-24": !effectiveCollapsed || mobileOpen
            })}>
              <img
                src="/assets/images/logo/logo_ghvn.png"
                alt="GHVN Logo"
                className="w-full h-full object-contain"
              />
            </div>
          </div>
        </div>

        {/* Navigation menu */}
        <div className="flex flex-col gap-1 flex-1 overflow-y-auto p-4">
          {/* Removed "MENU" text */}
          <div className="h-2"></div>

          <NavLink
            to="/dashboard"
            className={({isActive}) =>
              cn("sidebar-link", {
                active: isActive,
              })
            }
            onClick={closeMobile}
          >
            <LayoutDashboard className="h-5 w-5"/>
            {(!effectiveCollapsed || mobileOpen) && <span>Dashboard</span>}
          </NavLink>


          <NavLink
            to="/admins"
            className={({isActive}) =>
              cn("sidebar-link", {
                active: isActive || location.pathname.includes("/admins/"),
              })
            }
            onClick={closeMobile}
          >
            <Users className="h-5 w-5"/>
            {(!effectiveCollapsed || mobileOpen) && <span>Quản lý người dùng</span>}
          </NavLink>
          <NavLink
            to="/call-center"
            className={({isActive}) =>
              cn("sidebar-link", {
                active: isActive,
              })
            }
            onClick={closeMobile}
          >
            <PhoneCall className="h-5 w-5"/>
            {(!effectiveCollapsed || mobileOpen) && <span>Trung tâm cuộc gọi</span>}
          </NavLink>

          {/*<NavLink*/}
          {/*  to="/customers"*/}
          {/*  className={({isActive}) =>*/}
          {/*    cn("sidebar-link", {*/}
          {/*      active: isActive,*/}
          {/*    })*/}
          {/*  }*/}
          {/*  onClick={() => setMobileOpen(false)}*/}
          {/*>*/}
          {/*  <Users className="h-5 w-5"/>*/}
          {/*  {(!effectiveCollapsed || mobileOpen) && <span>Khách hàng</span>}*/}
          {/*</NavLink>*/}

          {/*<NavLink*/}
          {/*  to="/chat"*/}
          {/*  className={({isActive}) =>*/}
          {/*    cn("sidebar-link", {*/}
          {/*      active: isActive,*/}
          {/*    })*/}
          {/*  }*/}
          {/*  onClick={() => setMobileOpen(false)}*/}
          {/*>*/}
          {/*  <MessageSquare className="h-5 w-5"/>*/}
          {/*  {(!effectiveCollapsed || mobileOpen) && <span>Chat với khách hàng</span>}*/}
          {/*</NavLink>*/}

          <NavLink
            to="/customers-care"
            className={({isActive}) =>
              cn("sidebar-link", {
                active: isActive,
              })
            }
            onClick={closeMobile}
          >
            <MessageCircleMore className="h-5 w-5"/>
            {(!effectiveCollapsed || mobileOpen) && <span>Chăm sóc khách hàng</span>}
          </NavLink>
          <NavLink
            to="/config-openai"
            className={({isActive}) =>
              cn("sidebar-link", {
                active: isActive,
              })
            }
            onClick={closeMobile}
          >
            <BotMessageSquare className="h-5 w-5"/>
            {(!effectiveCollapsed || mobileOpen) && <span>Cấu hình AI</span>}
          </NavLink>
          <NavLink
            to="/knowledge"
            className={({isActive}) =>
              cn("sidebar-link", {
                active: isActive,
              })
            }
            onClick={closeMobile}
          >
            <FileSearch className="h-5 w-5"/>
            {(!effectiveCollapsed || mobileOpen) && <span>Tài liệu huấn luyện</span>}
          </NavLink>

          {/*<div className="ml-6 mt-1 flex flex-col gap-1">*/}
          {/*    <NavLink*/}
          {/*      to="/config-openai2"*/}
          {/*      className={({isActive}) =>*/}
          {/*        cn("sidebar-link text-sm py-2", {*/}
          {/*          active: isActive,*/}
          {/*        })*/}
          {/*      }*/}
          {/*      onClick={() => setMobileOpen(false)}*/}
          {/*    >*/}
          {/*      <span>Cấu hình kỹ thuật</span>*/}
          {/*    </NavLink>*/}

          {/*  <NavLink*/}
          {/*    to="/config-openai3"*/}
          {/*    className={({isActive}) =>*/}
          {/*      cn("sidebar-link text-sm py-2", {*/}
          {/*        active: isActive,*/}
          {/*      })*/}
          {/*    }*/}
          {/*    onClick={() => setMobileOpen(false)}*/}
          {/*  >*/}
          {/*    <span>Kiến thức truy xuất</span>*/}
          {/*  </NavLink>*/}
          {/*</div>*/}

        </div>


        {/*/!* Logout button *!/*/}
        {/*<div className="border-t border-border p-4">*/}
        {/*  <Button*/}
        {/*    variant="ghost"*/}
        {/*    className="w-full flex items-center justify-start gap-2"*/}
        {/*    onClick={logout}*/}
        {/*  >*/}
        {/*    <LogOut className="h-5 w-5"/>*/}
        {/*    {(!collapsed || mobileOpen) && <span>Đăng xuất</span>}*/}
        {/*  </Button>*/}
        {/*</div>*/}
      </div>
    </>
  );
}
