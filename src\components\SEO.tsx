import { Helmet } from 'react-helmet-async';
import { APP_CONFIG } from '@/config/env';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  ogImage?: string;
  ogUrl?: string;
}

export const SEO = ({
  title,
  description = APP_CONFIG.DESCRIPTION,
  keywords = APP_CONFIG.KEYWORDS,
  ogImage = APP_CONFIG.LOGO,
  ogUrl,
}: SEOProps) => {
  // Construct the full title with the site name
  const fullTitle = title ? `${title} | ${APP_CONFIG.NAME}` : APP_CONFIG.NAME;

  return (
    <Helmet>
      {/* Basic meta tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />

      {/* Open Graph / Facebook */}
      <meta property="og:type" content="website" />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      {ogImage && <meta property="og:image" content={ogImage} />}
      {ogUrl && <meta property="og:url" content={ogUrl} />}

      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      {ogImage && <meta name="twitter:image" content={ogImage} />}
    </Helmet>
  );
};
