import React, {useRef, useEffect} from "react";
import {ScrollArea} from "@/components/ui/scroll-area.tsx";
import {Button} from "@/components/ui/button.tsx";
import {useAuth} from "@/contexts/AuthContext.tsx";
import {Message, Conversation} from "@/services/CskhService.ts";
import {formatDate} from '@/lib/utils';
import MessageGroup from './MessageGroup.tsx'
import TopicChat from "@/pages/customer-care/customer/TopicChat";
import Loading from "@/components/custom/Loading";
import {ArrowLeft} from "lucide-react";


interface ChatContentProps {
  messages: Message[]
  showAvatar?: boolean
  messagesLoading?: boolean
  showNewConvo?: boolean
  setNotYetRead: (boolean: boolean) => void
  isSmallScreen?: boolean
  backToSidebar?: (room_id?: string) => void
  conversation?: Conversation
}

const ChatContent: React.FC<ChatContentProps> = ({
                                                   messages,
                                                   setNotYetRead,
                                                   showAvatar = false,
                                                   messagesLoading = false,
                                                   showNewConvo = false,
                                                   conversation,
                                                   isSmallScreen,
                                                   backToSidebar,
                                                 }) => {
  const {user} = useAuth();
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const isFirstRender = useRef(true);
  const memberOfRoom = (user._id === conversation?.staffId || user._id === conversation?.userId)
  const isUser = (user.role === "shop" || user.role === "buyer")
  useEffect(() => {
    isFirstRender.current = true;
  }, [conversation?._id]);

  useEffect(() => {
    const viewport = scrollAreaRef.current;
    if (!viewport) return;

    const scrollToBottom = () => {
      viewport.scrollTo({
        top: viewport.scrollHeight,
        behavior: isFirstRender.current ? "auto" : "smooth", // ✨
      });
    };

    scrollToBottom();
    isFirstRender.current = false;
  }, [messages]);




  const groupMessagesByDate = (messages: Message[]) => {
    let unread = []
    let read = messages
    if (memberOfRoom) {
      unread = messages.filter(msg => !msg.read && msg.senderId !== user._id);
      read = messages.filter(msg => (msg.read || msg.senderId === user._id));
    }

    const groups: { [key: string]: Message[] } = {};

    read.forEach(message => {
      const dateKey = new Date(message.createdAt).toDateString();
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(message);
    });

    const result = Object.entries(groups).map(([dateKey, messages]) => ({
      key: formatDate(dateKey),
      groupName: formatDate(dateKey),
      messages
    }));

    if (unread.length > 0 && memberOfRoom)
      result.push({key: "new_message", groupName: 'Tin nhắn mới', messages: unread})

    return result
  };

  const messageGroups = groupMessagesByDate(messages);

  useEffect(() => {
    const check = messageGroups.filter(i => i.key === "new_message")
    setNotYetRead(check.length > 0)
  }, [messageGroups]);

  if (showNewConvo && isUser) return (
    <div
      className="flex-1 flex flex-col items-center justify-center bg-gradient-to-r from-primary/5 to-primary/10 min-h-0">
      <div className="flex-1 w-full overflow-y-auto min-h-0">
        {isSmallScreen && <Button
            className="mt-2 ml-2"
            variant="ghost"
            onClick={() => backToSidebar()}
            size="sm"
        >
            <ArrowLeft size={16}/>
        </Button>}
        <TopicChat/>
      </div>
    </div>
  )

  if (conversation && messages.length === 0 && isUser) return (
    <div
      className="flex-1 flex flex-col items-center justify-center bg-white min-h-0">
      <div className="text-muted-foreground text-xl">
        Gửi một tin nhắn để bắt đầu
      </div>
    </div>
  )

  if (messagesLoading) return (<div className="flex-1">
      <Loading size="lg" text="Đang tải dữ liệu..."/>
    </div>

  )

  return (
    <ScrollArea className="flex-1 bg-transparent p-4 " ref={scrollAreaRef}>
      <div
        className="space-y-4"
      >
        {messageGroups.map(({key,groupName, messages}, index) => (
          <MessageGroup
            key={key}
            showAvatar={showAvatar}
            messages={messages}
            user={user}
            groupName={groupName}
            conversation={conversation}
          />
        ))}
        <div ref={messagesEndRef}/>
      </div>
    </ScrollArea>
  )

}

export default ChatContent;