import { io, Socket } from "socket.io-client";
import {MessageAttachment} from "@/services/ChatService.ts";

// Singleton socket instance
let socket: Socket | null = null;

/**
 * Khởi tạo kết nối socket
 */
export const initializeSocket = (): Socket => {
  if (!socket) {
    // Use relative path to leverage Vite proxy
    socket = io("/", {
      autoConnect: false, // Don't auto connect to avoid immediate errors
      reconnection: true,
      reconnectionAttempts: 3,
      reconnectionDelay: 2000,
      path: "/socket", // Match backend path
      timeout: 5000,
      forceNew: true,
      transports: ["websocket"], // Match backend transports
    });

    // Log kết nối
    socket.on("connect", () => {
      console.log("✅ Socket connected:", socket?.id);
    });

    socket.on("disconnect", (reason) => {
      console.log("🔌 Socket disconnected:", reason);
    });

    socket.on("connect_error", (error) => {
      console.warn("⚠️ Socket connection error:", error.message);
      console.log("💡 Đảm bảo backend socket server đang chạy trên port 3001 với path '/socket'");
    });

    socket.on("reconnect", (attemptNumber) => {
      console.log("🔄 Socket reconnected after", attemptNumber, "attempts");
    });

    socket.on("reconnect_error", (error) => {
      console.warn("❌ Socket reconnection failed:", error.message);
    });

    socket.on("reconnect_failed", () => {
      console.error("💥 Socket reconnection failed after all attempts");
    });
  }

  return socket;
};

/**
 * Kết nối socket một cách an toàn
 */
export const connectSocket = (): Promise<Socket> => {
  return new Promise((resolve, reject) => {
    if (!socket) {
      socket = initializeSocket();
    }

    if (socket.connected) {
      resolve(socket);
      return;
    }

    const timeout = setTimeout(() => {
      reject(new Error("Socket connection timeout"));
    }, 10000);

    socket.once("connect", () => {
      clearTimeout(timeout);
      resolve(socket!);
    });

    socket.once("connect_error", (error) => {
      clearTimeout(timeout);
      reject(error);
    });

    socket.connect();
  });
};

/**
 * Lấy socket instance hiện tại
 */
export const getSocket = (): Socket | null => {
  return socket;
};

/**
 * Kiểm tra trạng thái kết nối
 */
export const isSocketConnected = (): boolean => {
  return socket?.connected || false;
};

/**
 * Đóng kết nối socket
 */
export const disconnectSocket = (): void => {
  if (socket) {
    socket.disconnect();
    socket = null;
  }
};

/**
 * Join vào một conversation room
 */
export const joinConversation = (conversationId: string): void => {
  if (socket) {
    socket.emit("join", conversationId);
  }
};

/**
 * Leave khỏi một conversation room
 */
export const leaveConversation = (conversationId: string): void => {
  if (socket) {
    socket.emit("leave", conversationId);
  }
};

/**
 * Gửi tin nhắn qua socket
 */
export const sendMessage = (data: {
  conversationId: string;
  senderId: string;
  content: string;
  attachments?: MessageAttachment[];
  tempId?: string;
}): void => {
  if (socket) {
    console.log('📡 Emitting sendMessage:', data);
    socket.emit("sendMessage", data);
  }
};

export default {
  initializeSocket,
  connectSocket,
  getSocket,
  isSocketConnected,
  disconnectSocket,
  joinConversation,
  leaveConversation,
  sendMessage,
};
