import { Plus, Edit, Trash2, Check<PERSON>ir<PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

// Import các common component
import { DataTable } from "@/components/common/DataTable";
import { SearchBar } from "@/components/common/SearchBar";
import { PageHeader, PageHeaderAction } from "@/components/common/PageHeader";
import { ConfirmDeleteDialog } from "@/components/common/ConfirmDeleteDialog";
import { StatusBadge } from "@/components/common/StatusBadge";

// Import các component con
import { UserFormDialog } from "./components/UserFormDialog";

// Import custom hook và services
import { useAdministrators } from "./hooks/useAdministrators";
import { userService } from "@/services/UserService";
import { enhancedToast } from "@/components/common/EnhancedToast";

export default function Administrators() {
  const {
    administrators,
    loading,
    searchQuery,
    paginationInfo,
    dialogOpen,
    selectedUser,
    deleteDialogOpen,
    userToDelete,
    setDialog<PERSON><PERSON>,
    setDeleteDialog<PERSON><PERSON>,
    handleSearch,
    handlePageChange,
    handleAddNew,
    handleEdit,
    handleDeleteConfirm,
    fetchAdministrators
  } = useAdministrators();

  return (
    <div className="space-y-6">
      <PageHeader
        title="Danh sách người dùng"
        description="Quản lý danh sách người dùng trong hệ thống"
        actions={
          <PageHeaderAction
            icon={<Plus className="h-4 w-4" />}
            text="Thêm mới"
            onClick={handleAddNew}
          />
        }
      />

      {/* Thanh tìm kiếm */}
      <SearchBar
        value={searchQuery}
        onChange={(value) => handleSearch({ target: { value } } as React.ChangeEvent<HTMLInputElement>)}
        placeholder="Tìm kiếm người dùng..."
        showClearButton={true}
        useDebounce={true}
        debounceTime={300}
      />

      {/* Bảng danh sách quản trị viên */}
      <DataTable
        data={administrators}
        loading={loading}
        paginationInfo={paginationInfo}
        onPageChange={handlePageChange}
        itemName="người dùng"
        showFirstLastButtons={true}
        emptyMessage="Không có dữ liệu người dùng"
        columns={[
          {
            header: "STT",
            cell: (_, index) => index + 1 + (paginationInfo.page - 1) * paginationInfo.limit,
            className: "w-[50px]"
          },
          {
            header: "Tên",
            accessorKey: "fullName"
          },
          {
            header: "Email",
            accessorKey: "email"
          },
          {
            header: "Vai trò",
            accessorKey: "role"
          },
          {
            header: "Trạng thái",
            cell: (admin) => (
              admin.status === "active"
                ? <StatusBadge variant="success">Hoạt động</StatusBadge>
                : <StatusBadge variant="default">Không hoạt động</StatusBadge>
            )
          },
          {
            header: "Thao tác",
            className: "text-right",
            cell: (admin) => (
              <div className="flex justify-end gap-2">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleEdit(admin)}
                  title="Chỉnh sửa"
                >
                  <Edit className="h-4 w-4"/>
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  title="Xóa"
                  onClick={() => handleDeleteConfirm(admin)}
                >
                  <Trash2 className="h-4 w-4 text-destructive"/>
                </Button>
              </div>
            )
          }
        ]}
      />

      {/* Dialog thêm/sửa quản trị viên */}
      <UserFormDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        user={selectedUser}
        onSuccess={fetchAdministrators}
      />

      {/* Dialog xác nhận xóa */}
      <ConfirmDeleteDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="Xác nhận xóa người dùng"
        description={
          <>
            Bạn có chắc chắn muốn xóa người dùng {userToDelete?.fullName} ({userToDelete?.email}) không?
            <br />
            Hành động này không thể hoàn tác.
          </>
        }
        onConfirm={async () => {
          if (!userToDelete) return;
          await userService.deleteUser(userToDelete._id);
          fetchAdministrators();
        }}
        successMessage={userToDelete ? `Đã xóa người dùng ${userToDelete.fullName} thành công!` : "Xóa người dùng thành công!"}
      />
    </div>
  );
}
